"""
Character System - Manages story characters with consistency tracking
Handles character personalities, appearances, voices, and location tracking
"""

import logging
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)


class CharacterRole(Enum):
    """Character roles in the story"""
    PROTAGONIST = "protagonist"
    ANTAGONIST = "antagonist"
    ALLY = "ally"
    NEUTRAL = "neutral"
    MENTOR = "mentor"
    LOVE_INTEREST = "love_interest"
    COMIC_RELIEF = "comic_relief"
    BACKGROUND = "background"


class CharacterStatus(Enum):
    """Character status in the story"""
    ALIVE = "alive"
    DEAD = "dead"
    MISSING = "missing"
    TRANSFORMED = "transformed"
    UNKNOWN = "unknown"


@dataclass
class CharacterAppearance:
    """Character visual appearance description"""
    physical_description: str = ""
    clothing: str = ""
    distinctive_features: str = ""
    age_range: str = ""
    height: str = ""
    build: str = ""
    hair_color: str = ""
    eye_color: str = ""
    skin_tone: str = ""
    reference_image_path: Optional[str] = None
    
    def get_visual_prompt(self) -> str:
        """Generate visual prompt for image generation"""
        parts = []
        if self.physical_description:
            parts.append(self.physical_description)
        if self.age_range:
            parts.append(f"{self.age_range} years old")
        if self.hair_color:
            parts.append(f"{self.hair_color} hair")
        if self.eye_color:
            parts.append(f"{self.eye_color} eyes")
        if self.clothing:
            parts.append(f"wearing {self.clothing}")
        if self.distinctive_features:
            parts.append(self.distinctive_features)
        
        return ", ".join(parts) if parts else "person"


@dataclass
class CharacterVoice:
    """Character voice and speech patterns"""
    voice_description: str = ""
    accent: str = ""
    speech_patterns: List[str] = field(default_factory=list)
    vocabulary_style: str = ""  # formal, casual, archaic, etc.
    voice_file_path: Optional[str] = None
    tts_voice_id: Optional[str] = None
    pitch_modifier: float = 1.0
    speed_modifier: float = 1.0
    
    def get_speech_style_prompt(self) -> str:
        """Generate speech style prompt for dialogue generation"""
        parts = []
        if self.voice_description:
            parts.append(self.voice_description)
        if self.accent:
            parts.append(f"with {self.accent} accent")
        if self.vocabulary_style:
            parts.append(f"using {self.vocabulary_style} vocabulary")
        if self.speech_patterns:
            parts.append(f"speech patterns: {', '.join(self.speech_patterns)}")
        
        return "; ".join(parts) if parts else "neutral voice"


@dataclass
class CharacterPersonality:
    """Character personality traits and motivations"""
    traits: List[str] = field(default_factory=list)
    motivations: List[str] = field(default_factory=list)
    fears: List[str] = field(default_factory=list)
    goals: List[str] = field(default_factory=list)
    relationships: Dict[str, str] = field(default_factory=dict)  # character_id -> relationship
    moral_alignment: str = ""  # good, evil, neutral, chaotic, lawful, etc.
    background_story: str = ""
    quirks: List[str] = field(default_factory=list)
    
    def get_personality_prompt(self) -> str:
        """Generate personality prompt for dialogue/action generation"""
        parts = []
        if self.traits:
            parts.append(f"personality: {', '.join(self.traits)}")
        if self.motivations:
            parts.append(f"motivated by: {', '.join(self.motivations)}")
        if self.moral_alignment:
            parts.append(f"moral alignment: {self.moral_alignment}")
        if self.quirks:
            parts.append(f"quirks: {', '.join(self.quirks)}")
        
        return "; ".join(parts) if parts else "neutral personality"


@dataclass
class CharacterState:
    """Character state at a specific story node"""
    node_id: str
    is_present: bool = False
    status: CharacterStatus = CharacterStatus.ALIVE
    location: str = ""
    emotional_state: str = ""
    knowledge_state: Dict[str, bool] = field(default_factory=dict)  # what they know
    inventory: List[str] = field(default_factory=list)
    relationships_state: Dict[str, str] = field(default_factory=dict)  # current relationships
    notes: str = ""


@dataclass
class Character:
    """Complete character definition"""
    id: str
    name: str
    role: CharacterRole
    appearance: CharacterAppearance = field(default_factory=CharacterAppearance)
    voice: CharacterVoice = field(default_factory=CharacterVoice)
    personality: CharacterPersonality = field(default_factory=CharacterPersonality)
    
    # Story tracking
    first_appearance_node: Optional[str] = None
    last_seen_node: Optional[str] = None
    states: Dict[str, CharacterState] = field(default_factory=dict)  # node_id -> state
    
    # Metadata
    created_at: str = ""
    tags: List[str] = field(default_factory=list)
    is_player_character: bool = False
    importance_level: int = 1  # 1-5, 5 being most important
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_at:
            from datetime import datetime
            self.created_at = datetime.now().isoformat()
    
    def get_state_at_node(self, node_id: str) -> Optional[CharacterState]:
        """Get character state at specific node"""
        return self.states.get(node_id)
    
    def is_present_at_node(self, node_id: str) -> bool:
        """Check if character is present at a node"""
        state = self.get_state_at_node(node_id)
        return state.is_present if state else False
    
    def get_full_description(self) -> str:
        """Get complete character description for AI prompts"""
        parts = [
            f"Character: {self.name}",
            f"Role: {self.role.value}",
            f"Appearance: {self.appearance.get_visual_prompt()}",
            f"Personality: {self.personality.get_personality_prompt()}",
            f"Voice: {self.voice.get_speech_style_prompt()}"
        ]
        
        return "\n".join(parts)


class CharacterManager:
    """Manages all characters in a story"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.characters: Dict[str, Character] = {}
        self.character_config = config.get('character_system', {})
        
        # Character tracking
        self.node_characters: Dict[str, Set[str]] = {}  # node_id -> character_ids
        self.character_relationships: Dict[Tuple[str, str], str] = {}  # (char1, char2) -> relationship
        
    def create_character(self, name: str, role: CharacterRole, **kwargs) -> Character:
        """Create a new character"""
        character = Character(
            id=str(uuid.uuid4()),
            name=name,
            role=role,
            **kwargs
        )
        
        self.characters[character.id] = character
        logger.info(f"Created character: {name} ({role.value})")
        
        return character
    
    def add_character(self, character: Character) -> bool:
        """Add an existing character"""
        try:
            self.characters[character.id] = character
            logger.info(f"Added character: {character.name}")
            return True
        except Exception as e:
            logger.error(f"Error adding character: {e}")
            return False
    
    def get_character(self, character_id: str) -> Optional[Character]:
        """Get character by ID"""
        return self.characters.get(character_id)
    
    def get_character_by_name(self, name: str) -> Optional[Character]:
        """Get character by name"""
        for character in self.characters.values():
            if character.name.lower() == name.lower():
                return character
        return None
    
    def update_character_state(self, character_id: str, node_id: str, 
                             state_updates: Dict[str, Any]) -> bool:
        """Update character state at a specific node"""
        try:
            character = self.get_character(character_id)
            if not character:
                logger.error(f"Character not found: {character_id}")
                return False
            
            # Get or create state for this node
            if node_id not in character.states:
                character.states[node_id] = CharacterState(node_id=node_id)
            
            state = character.states[node_id]
            
            # Update state fields
            for field, value in state_updates.items():
                if hasattr(state, field):
                    setattr(state, field, value)
            
            # Update node character tracking
            if state.is_present:
                if node_id not in self.node_characters:
                    self.node_characters[node_id] = set()
                self.node_characters[node_id].add(character_id)
                
                # Update last seen
                character.last_seen_node = node_id
            else:
                if node_id in self.node_characters:
                    self.node_characters[node_id].discard(character_id)
            
            logger.debug(f"Updated character {character.name} state at node {node_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating character state: {e}")
            return False
    
    def get_characters_at_node(self, node_id: str) -> List[Character]:
        """Get all characters present at a node"""
        character_ids = self.node_characters.get(node_id, set())
        return [self.characters[char_id] for char_id in character_ids 
                if char_id in self.characters]
    
    def get_character_context_for_node(self, node_id: str) -> Dict[str, Any]:
        """Get character context for story generation at a node"""
        present_characters = self.get_characters_at_node(node_id)
        
        context = {
            "present_characters": [],
            "character_relationships": {},
            "location_context": {},
            "emotional_context": {}
        }
        
        for character in present_characters:
            state = character.get_state_at_node(node_id)
            
            char_info = {
                "name": character.name,
                "role": character.role.value,
                "description": character.get_full_description(),
                "emotional_state": state.emotional_state if state else "",
                "location": state.location if state else "",
                "status": state.status.value if state else "alive"
            }
            
            context["present_characters"].append(char_info)
            
            # Add relationship context
            if state and state.relationships_state:
                context["character_relationships"][character.name] = state.relationships_state
        
        return context
    
    def analyze_character_consistency(self, story_web) -> Dict[str, List[str]]:
        """Analyze character consistency across the story"""
        issues = {}
        
        for character in self.characters.values():
            char_issues = []
            
            # Check for appearance in disconnected nodes
            present_nodes = [node_id for node_id, state in character.states.items() 
                           if state.is_present]
            
            if len(present_nodes) > 1:
                # Check if character appearances make logical sense
                for i in range(len(present_nodes) - 1):
                    current_node = present_nodes[i]
                    next_node = present_nodes[i + 1]
                    
                    # Check if there's a path between nodes
                    if not self._has_path_between_nodes(story_web, current_node, next_node):
                        char_issues.append(f"Character appears in disconnected nodes: {current_node} -> {next_node}")
            
            # Check for status consistency
            status_changes = []
            for node_id in sorted(character.states.keys()):
                state = character.states[node_id]
                if state.status != CharacterStatus.ALIVE:
                    status_changes.append((node_id, state.status))
            
            # Check if character appears after death
            for i, (death_node, status) in enumerate(status_changes):
                if status == CharacterStatus.DEAD:
                    for node_id, state in character.states.items():
                        if state.is_present and node_id != death_node:
                            # This would need proper story flow analysis
                            pass  # Simplified for now
            
            if char_issues:
                issues[character.name] = char_issues
        
        return issues
    
    def _has_path_between_nodes(self, story_web, node1: str, node2: str) -> bool:
        """Check if there's a path between two nodes (simplified)"""
        try:
            import networkx as nx
            return nx.has_path(story_web.graph, node1, node2)
        except:
            return True  # Assume connected if can't check
    
    def generate_character_dialogue(self, character_id: str, context: str, 
                                  ollama_client) -> Optional[str]:
        """Generate character-specific dialogue"""
        try:
            character = self.get_character(character_id)
            if not character:
                return None
            
            # Create character-specific prompt
            prompt = f"""
Generate dialogue for this character in the given context:

{character.get_full_description()}

Context: {context}

Generate dialogue that matches their personality, speech patterns, and current emotional state.
Keep it consistent with their established character traits.
"""
            
            response = ollama_client.generate_text(prompt, max_tokens=200)
            if response.success:
                return response.text.strip()
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating character dialogue: {e}")
            return None
    
    def suggest_character_actions(self, character_id: str, node_context: str,
                                ollama_client) -> List[str]:
        """Suggest actions a character might take based on their personality"""
        try:
            character = self.get_character(character_id)
            if not character:
                return []
            
            prompt = f"""
Based on this character's personality and the current situation, suggest 3-5 actions they might take:

{character.get_full_description()}

Current situation: {node_context}

List actions that would be consistent with their personality, motivations, and role in the story.
"""
            
            response = ollama_client.generate_text(prompt, max_tokens=300)
            if response.success:
                # Parse actions from response
                actions = []
                for line in response.text.split('\n'):
                    line = line.strip()
                    if line and (line.startswith('-') or line.startswith('•') or line[0].isdigit()):
                        # Clean up action text
                        action = line.lstrip('-•0123456789. ').strip()
                        if action:
                            actions.append(action)
                
                return actions[:5]  # Limit to 5 actions
            
            return []
            
        except Exception as e:
            logger.error(f"Error suggesting character actions: {e}")
            return []
    
    def export_character_data(self, character_id: str) -> Optional[Dict[str, Any]]:
        """Export character data for external use"""
        character = self.get_character(character_id)
        if not character:
            return None
        
        return asdict(character)
    
    def import_character_data(self, character_data: Dict[str, Any]) -> bool:
        """Import character data"""
        try:
            # Convert enum strings back to enums
            if 'role' in character_data:
                character_data['role'] = CharacterRole(character_data['role'])
            
            # Reconstruct nested objects
            if 'appearance' in character_data:
                character_data['appearance'] = CharacterAppearance(**character_data['appearance'])
            
            if 'voice' in character_data:
                character_data['voice'] = CharacterVoice(**character_data['voice'])
            
            if 'personality' in character_data:
                character_data['personality'] = CharacterPersonality(**character_data['personality'])
            
            # Reconstruct states
            if 'states' in character_data:
                states = {}
                for node_id, state_data in character_data['states'].items():
                    if 'status' in state_data:
                        state_data['status'] = CharacterStatus(state_data['status'])
                    states[node_id] = CharacterState(**state_data)
                character_data['states'] = states
            
            character = Character(**character_data)
            return self.add_character(character)
            
        except Exception as e:
            logger.error(f"Error importing character data: {e}")
            return False
    
    def get_character_summary(self) -> Dict[str, Any]:
        """Get summary of all characters"""
        summary = {
            "total_characters": len(self.characters),
            "by_role": {},
            "by_importance": {},
            "most_active": [],
            "character_list": []
        }
        
        # Count by role
        for character in self.characters.values():
            role = character.role.value
            summary["by_role"][role] = summary["by_role"].get(role, 0) + 1
            
            # Count by importance
            importance = character.importance_level
            summary["by_importance"][importance] = summary["by_importance"].get(importance, 0) + 1
            
            # Character info
            summary["character_list"].append({
                "name": character.name,
                "role": role,
                "importance": importance,
                "nodes_present": len([s for s in character.states.values() if s.is_present])
            })
        
        # Sort by activity (nodes present)
        summary["character_list"].sort(key=lambda x: x["nodes_present"], reverse=True)
        summary["most_active"] = summary["character_list"][:5]
        
        return summary
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert character manager to dictionary"""
        return {
            "characters": {
                char_id: asdict(character) 
                for char_id, character in self.characters.items()
            },
            "node_characters": {
                node_id: list(char_set) 
                for node_id, char_set in self.node_characters.items()
            },
            "character_relationships": {
                f"{char1}_{char2}": relationship 
                for (char1, char2), relationship in self.character_relationships.items()
            }
        }
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any], data: Dict[str, Any]) -> 'CharacterManager':
        """Create character manager from dictionary"""
        manager = cls(config)
        
        # Load characters
        if "characters" in data:
            for char_id, char_data in data["characters"].items():
                if manager.import_character_data(char_data):
                    # Update the ID to match the key
                    if char_id in manager.characters:
                        manager.characters[char_id].id = char_id
        
        # Load node character mappings
        if "node_characters" in data:
            for node_id, char_list in data["node_characters"].items():
                manager.node_characters[node_id] = set(char_list)
        
        # Load relationships
        if "character_relationships" in data:
            for key, relationship in data["character_relationships"].items():
                char1, char2 = key.split("_", 1)
                manager.character_relationships[(char1, char2)] = relationship
        
        return manager
