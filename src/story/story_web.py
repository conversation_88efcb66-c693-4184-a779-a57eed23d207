"""
Story Web - Core DAG structure for CYOA stories
Manages nodes, connections, and story flow with inventory and class systems
"""

import json
import uuid
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import networkx as nx
import logging

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Types of story nodes"""
    ENTRY = "entry"
    STORY = "story"
    CHOICE = "choice"
    ENDING = "ending"
    SIDE_MISSION = "side_mission"
    INTERMEDIARY = "intermediary"


class EndingType(Enum):
    """Types of story endings"""
    DEATH = "death"
    SUCCESS = "success"
    NEUTRAL = "neutral"


@dataclass
class Choice:
    """Represents a choice option in a story node"""
    id: str
    text: str
    target_node_id: str
    inventory_requirements: Dict[str, int] = None
    class_requirements: List[str] = None
    inventory_changes: Dict[str, int] = None
    is_premium: bool = False
    is_spicy: bool = False
    
    def __post_init__(self):
        if self.inventory_requirements is None:
            self.inventory_requirements = {}
        if self.class_requirements is None:
            self.class_requirements = []
        if self.inventory_changes is None:
            self.inventory_changes = {}


@dataclass
class StoryNode:
    """Represents a single node in the story web"""
    id: str
    text: str
    node_type: NodeType
    choices: List[Choice] = None
    is_entry: bool = False
    is_ending: bool = False
    ending_type: Optional[EndingType] = None
    is_premium: bool = False
    rating: str = "safe"  # "safe", "spicy"
    score: Optional[float] = None
    inventory_state: Dict[str, int] = None
    class_context: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.choices is None:
            self.choices = []
        if self.inventory_state is None:
            self.inventory_state = {}
        if self.metadata is None:
            self.metadata = {}


class StoryWeb:
    """
    Manages the complete story web as a directed acyclic graph (DAG)
    Handles inventory tracking, class systems, and story flow
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.nodes: Dict[str, StoryNode] = {}
        self.graph = nx.DiGraph()
        self.entry_points: List[str] = []
        self.endings: List[str] = []
        self.metadata = {
            "title": "",
            "description": "",
            "created_at": "",
            "version": "1.0"
        }
    
    def add_node(self, node: StoryNode) -> bool:
        """Add a node to the story web"""
        try:
            if node.id in self.nodes:
                logger.warning(f"Node {node.id} already exists, updating")
            
            self.nodes[node.id] = node
            self.graph.add_node(node.id, **asdict(node))
            
            # Track entry points and endings
            if node.is_entry:
                if node.id not in self.entry_points:
                    self.entry_points.append(node.id)
            
            if node.is_ending:
                if node.id not in self.endings:
                    self.endings.append(node.id)
            
            return True
        except Exception as e:
            logger.error(f"Error adding node {node.id}: {e}")
            return False
    
    def add_choice(self, from_node_id: str, choice: Choice) -> bool:
        """Add a choice connection between nodes"""
        try:
            if from_node_id not in self.nodes:
                logger.error(f"Source node {from_node_id} not found")
                return False
            
            if choice.target_node_id not in self.nodes:
                logger.error(f"Target node {choice.target_node_id} not found")
                return False
            
            # Add choice to source node
            self.nodes[from_node_id].choices.append(choice)
            
            # Add edge to graph
            self.graph.add_edge(
                from_node_id, 
                choice.target_node_id,
                choice_id=choice.id,
                choice_text=choice.text,
                is_premium=choice.is_premium,
                is_spicy=choice.is_spicy
            )
            
            return True
        except Exception as e:
            logger.error(f"Error adding choice from {from_node_id}: {e}")
            return False
    
    def validate_structure(self) -> Tuple[bool, List[str]]:
        """Validate the story web structure"""
        errors = []
        
        # Check for cycles
        if not nx.is_directed_acyclic_graph(self.graph):
            errors.append("Story web contains cycles")
        
        # Check entry points
        if len(self.entry_points) < self.config.get('num_entry_points', 1):
            errors.append(f"Insufficient entry points: {len(self.entry_points)}")
        
        # Check endings
        min_endings = self.config.get('min_endings', 1)
        if len(self.endings) < min_endings:
            errors.append(f"Insufficient endings: {len(self.endings)}")
        
        # Check connectivity
        for node_id in self.nodes:
            if not self.is_reachable_from_entries(node_id):
                errors.append(f"Node {node_id} is not reachable from entry points")
        
        # Check choice limits
        max_choices = self.config.get('max_choices_per_node', 5)
        for node in self.nodes.values():
            if len(node.choices) > max_choices:
                errors.append(f"Node {node.id} has too many choices: {len(node.choices)}")
        
        return len(errors) == 0, errors
    
    def is_reachable_from_entries(self, node_id: str) -> bool:
        """Check if a node is reachable from any entry point"""
        for entry_id in self.entry_points:
            if nx.has_path(self.graph, entry_id, node_id):
                return True
        return False
    
    def calculate_scores(self) -> None:
        """Calculate scores for death endings"""
        try:
            for node in self.nodes.values():
                if node.ending_type == EndingType.DEATH:
                    node.score = self._calculate_death_score(node.id)
        except Exception as e:
            logger.error(f"Error calculating scores: {e}")
    
    def _calculate_death_score(self, death_node_id: str) -> float:
        """Calculate score for a death ending"""
        try:
            # Find shortest path from any entry to this death
            min_death_path = float('inf')
            for entry_id in self.entry_points:
                if nx.has_path(self.graph, entry_id, death_node_id):
                    path_length = nx.shortest_path_length(self.graph, entry_id, death_node_id)
                    min_death_path = min(min_death_path, path_length)
            
            # Find longest path to any success ending
            max_success_path = 0
            for node in self.nodes.values():
                if node.ending_type == EndingType.SUCCESS:
                    for entry_id in self.entry_points:
                        if nx.has_path(self.graph, entry_id, node.id):
                            path_length = nx.shortest_path_length(self.graph, entry_id, node.id)
                            max_success_path = max(max_success_path, path_length)
            
            if max_success_path == 0:
                return 0.0
            
            return (min_death_path / max_success_path) * 100
        except Exception as e:
            logger.error(f"Error calculating death score for {death_node_id}: {e}")
            return 0.0
    
    def get_available_choices(self, node_id: str, inventory: Dict[str, int], 
                            player_class: str) -> List[Choice]:
        """Get choices available to player based on inventory and class"""
        if node_id not in self.nodes:
            return []
        
        available_choices = []
        for choice in self.nodes[node_id].choices:
            # Check inventory requirements
            if self._meets_inventory_requirements(inventory, choice.inventory_requirements):
                # Check class requirements
                if not choice.class_requirements or player_class in choice.class_requirements:
                    available_choices.append(choice)
        
        return available_choices
    
    def _meets_inventory_requirements(self, inventory: Dict[str, int], 
                                    requirements: Dict[str, int]) -> bool:
        """Check if inventory meets requirements"""
        for item, required_amount in requirements.items():
            if inventory.get(item, 0) < required_amount:
                return False
        return True
    
    def apply_choice_effects(self, inventory: Dict[str, int], 
                           choice: Choice) -> Dict[str, int]:
        """Apply inventory changes from a choice"""
        new_inventory = inventory.copy()
        for item, change in choice.inventory_changes.items():
            new_inventory[item] = new_inventory.get(item, 0) + change
            # Ensure non-negative values
            new_inventory[item] = max(0, new_inventory[item])
        return new_inventory
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert story web to dictionary for serialization"""
        def serialize_node(node):
            node_dict = asdict(node)
            # Convert enums to strings
            if 'node_type' in node_dict and node_dict['node_type']:
                node_dict['node_type'] = node_dict['node_type'].value
            if 'ending_type' in node_dict and node_dict['ending_type']:
                node_dict['ending_type'] = node_dict['ending_type'].value
            return node_dict

        return {
            "metadata": self.metadata,
            "config": self.config,
            "entry_points": self.entry_points,
            "endings": self.endings,
            "nodes": {
                node_id: {
                    **serialize_node(node),
                    "choices": [asdict(choice) for choice in node.choices]
                }
                for node_id, node in self.nodes.items()
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StoryWeb':
        """Create story web from dictionary"""
        web = cls(data.get("config", {}))
        web.metadata = data.get("metadata", {})
        web.entry_points = data.get("entry_points", [])
        web.endings = data.get("endings", [])
        
        # Reconstruct nodes
        for node_id, node_data in data.get("nodes", {}).items():
            choices_data = node_data.pop("choices", [])
            
            # Convert enum strings back to enums
            if "node_type" in node_data:
                node_data["node_type"] = NodeType(node_data["node_type"])
            if "ending_type" in node_data and node_data["ending_type"]:
                node_data["ending_type"] = EndingType(node_data["ending_type"])
            
            node = StoryNode(**node_data)
            
            # Reconstruct choices
            for choice_data in choices_data:
                choice = Choice(**choice_data)
                node.choices.append(choice)
            
            web.add_node(node)
        
        # Rebuild graph connections
        for node in web.nodes.values():
            for choice in node.choices:
                web.graph.add_edge(node.id, choice.target_node_id)
        
        return web
    
    def save_to_file(self, filepath: str) -> bool:
        """Save story web to JSON file"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info(f"Story web saved to {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error saving story web to {filepath}: {e}")
            return False
    
    @classmethod
    def load_from_file(cls, filepath: str) -> Optional['StoryWeb']:
        """Load story web from JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            web = cls.from_dict(data)
            logger.info(f"Story web loaded from {filepath}")
            return web
        except Exception as e:
            logger.error(f"Error loading story web from {filepath}: {e}")
            return None
