"""
Video Generator - Creates videos for CYOA story nodes
Handles ComfyUI integration, prompt generation, and video processing
"""

import logging
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
import moviepy.editor as mp
from PIL import Image, ImageDraw, ImageFont
import numpy as np

from .comfyui_client import ComfyUIClient
from ..utils.ollama_client import OllamaClient

logger = logging.getLogger(__name__)


class VideoGenerator:
    """Generates videos for CYOA story nodes"""
    
    def __init__(self, config: Dict[str, Any], ollama_client: OllamaClient, 
                 comfyui_client: Optional[ComfyUIClient] = None):
        self.config = config
        self.ollama_client = ollama_client
        self.comfyui_client = comfyui_client
        
        self.media_config = config.get('media_generation', {})
        self.video_quality = self.media_config.get('video_quality', '720p')
        self.max_file_size_mb = self.media_config.get('max_file_size_mb', 15)
        self.video_duration = self.media_config.get('video_duration_seconds', 10)
        
        self.videos_dir = Path(config.get('paths', {}).get('videos_dir', 'videos'))
        self.videos_dir.mkdir(parents=True, exist_ok=True)
        
        # Video dimensions based on quality
        self.dimensions = {
            '480p': (854, 480),
            '720p': (1280, 720),
            '1080p': (1920, 1080)
        }
        
        self.width, self.height = self.dimensions.get(self.video_quality, (1280, 720))
        
    def generate_video_for_node(self, story_node, choices: List[Any], 
                               progress_callback: Optional[Callable] = None) -> Optional[str]:
        """Generate video for a story node with choices"""
        try:
            node_id = story_node.id
            logger.info(f"Generating video for node: {node_id}")
            
            if progress_callback:
                progress_callback(10, "Generating visual prompt...")
            
            # Generate visual prompt
            visual_prompt = self._generate_visual_prompt(story_node, choices)
            if not visual_prompt:
                logger.error("Failed to generate visual prompt")
                return None
            
            if progress_callback:
                progress_callback(30, "Creating background image...")
            
            # Generate or create background image
            background_path = self._create_background_image(visual_prompt, node_id)
            if not background_path:
                logger.error("Failed to create background image")
                return None
            
            if progress_callback:
                progress_callback(60, "Creating choice overlays...")
            
            # Create choice overlays
            choice_overlays = self._create_choice_overlays(choices, story_node)
            
            if progress_callback:
                progress_callback(80, "Compositing video...")
            
            # Create final video
            video_path = self._create_video_with_choices(
                background_path, choice_overlays, node_id, story_node
            )
            
            if progress_callback:
                progress_callback(100, "Video generation complete")
            
            logger.info(f"Generated video: {video_path}")
            return video_path
            
        except Exception as e:
            logger.error(f"Error generating video for node {node_id}: {e}")
            return None
    
    def _generate_visual_prompt(self, story_node, choices: List[Any]) -> Optional[str]:
        """Generate visual prompt for the story node"""
        try:
            # Prepare context
            choice_texts = [choice.text for choice in choices[:3]]  # Limit to 3 choices
            spicy_context = " Include romantic or sensual elements." if story_node.rating == "spicy" else ""
            class_context = f" Character is a {story_node.class_context}." if story_node.class_context else ""
            
            # Generate prompt using Ollama
            response = self.ollama_client.generate_video_prompt(
                story_node.text, 
                ", ".join(choice_texts),
                story_node.class_context or "Adventurer",
                story_node.rating == "spicy"
            )
            
            if response.success:
                return response.text
            else:
                logger.error(f"Failed to generate visual prompt: {response.error}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating visual prompt: {e}")
            return None
    
    def _create_background_image(self, visual_prompt: str, node_id: str) -> Optional[str]:
        """Create background image using ComfyUI or fallback"""
        try:
            output_path = self.videos_dir / f"{node_id}_background.png"
            
            # Try ComfyUI first
            if self.comfyui_client and self.comfyui_client.is_available():
                workflow_path = "workflows/video_generation.json"
                if Path(workflow_path).exists():
                    success = self.comfyui_client.generate_image(
                        workflow_path, visual_prompt, str(output_path),
                        width=self.width, height=self.height
                    )
                    if success:
                        return str(output_path)
            
            # Fallback: Create simple background
            logger.info("Using fallback background generation")
            return self._create_fallback_background(visual_prompt, str(output_path))
            
        except Exception as e:
            logger.error(f"Error creating background image: {e}")
            return None
    
    def _create_fallback_background(self, prompt: str, output_path: str) -> str:
        """Create a simple fallback background"""
        try:
            # Create gradient background
            image = Image.new('RGB', (self.width, self.height), color='#2c3e50')
            draw = ImageDraw.Draw(image)
            
            # Add gradient effect
            for y in range(self.height):
                alpha = y / self.height
                color = (
                    int(44 + alpha * 30),   # R
                    int(62 + alpha * 40),   # G
                    int(80 + alpha * 50)    # B
                )
                draw.line([(0, y), (self.width, y)], fill=color)
            
            # Add text overlay with prompt keywords
            try:
                font = ImageFont.truetype("Arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            # Extract key words from prompt
            keywords = prompt.split()[:5]  # First 5 words
            text = " ".join(keywords)
            
            # Add text with shadow
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            x = (self.width - text_width) // 2
            y = self.height // 3
            
            # Shadow
            draw.text((x + 2, y + 2), text, fill='black', font=font)
            # Main text
            draw.text((x, y), text, fill='white', font=font)
            
            # Save image
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            image.save(output_path)
            
            logger.info(f"Created fallback background: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error creating fallback background: {e}")
            # Create minimal background
            image = Image.new('RGB', (self.width, self.height), color='#34495e')
            image.save(output_path)
            return output_path
    
    def _create_choice_overlays(self, choices: List[Any], story_node) -> List[Dict[str, Any]]:
        """Create overlay images for choices"""
        overlays = []
        
        try:
            # Limit to 5 choices (X media limit)
            limited_choices = choices[:5]
            
            for i, choice in enumerate(limited_choices):
                overlay_info = self._create_choice_overlay(choice, i, len(limited_choices), story_node)
                if overlay_info:
                    overlays.append(overlay_info)
            
            return overlays
            
        except Exception as e:
            logger.error(f"Error creating choice overlays: {e}")
            return []
    
    def _create_choice_overlay(self, choice, index: int, total_choices: int, story_node) -> Optional[Dict[str, Any]]:
        """Create overlay for a single choice"""
        try:
            # Create overlay image
            overlay_width = self.width // 2
            overlay_height = 80
            
            overlay = Image.new('RGBA', (overlay_width, overlay_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # Choice background
            bg_color = (255, 100, 100, 200) if choice.is_spicy else (100, 150, 255, 200)
            if hasattr(choice, 'is_premium') and choice.is_premium:
                bg_color = (255, 215, 0, 200)  # Gold for premium
            
            draw.rounded_rectangle(
                [(5, 5), (overlay_width - 5, overlay_height - 5)],
                radius=10,
                fill=bg_color
            )
            
            # Choice text
            try:
                font = ImageFont.truetype("Arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            # Format choice text
            choice_text = choice.text
            if choice.is_spicy:
                choice_text = f"[Spicy] {choice_text}"
            if hasattr(choice, 'is_premium') and choice.is_premium:
                choice_text = f"[Premium] {choice_text}"
            
            # Wrap text if too long
            if len(choice_text) > 40:
                choice_text = choice_text[:37] + "..."
            
            # Center text
            text_bbox = draw.textbbox((0, 0), choice_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            x = (overlay_width - text_width) // 2
            y = (overlay_height - text_height) // 2
            
            # Text with shadow
            draw.text((x + 1, y + 1), choice_text, fill='black', font=font)
            draw.text((x, y), choice_text, fill='white', font=font)
            
            # Position on screen
            positions = [
                (50, self.height - 200),           # Bottom left
                (self.width - overlay_width - 50, self.height - 200),  # Bottom right
                (50, self.height - 300),           # Middle left
                (self.width - overlay_width - 50, self.height - 300),  # Middle right
                (self.width // 2 - overlay_width // 2, self.height - 150)  # Bottom center
            ]
            
            position = positions[index % len(positions)]
            
            return {
                'image': overlay,
                'position': position,
                'choice': choice,
                'duration': self.video_duration
            }
            
        except Exception as e:
            logger.error(f"Error creating choice overlay: {e}")
            return None
    
    def _create_video_with_choices(self, background_path: str, choice_overlays: List[Dict[str, Any]], 
                                 node_id: str, story_node) -> Optional[str]:
        """Create final video with background and choice overlays"""
        try:
            output_path = self.videos_dir / f"{node_id}_video.mp4"
            
            # Load background image
            background_clip = mp.ImageClip(background_path, duration=self.video_duration)
            
            # Create choice overlay clips
            overlay_clips = []
            for overlay_info in choice_overlays:
                # Save overlay to temp file
                temp_overlay_path = self.videos_dir / f"{node_id}_overlay_{len(overlay_clips)}.png"
                overlay_info['image'].save(temp_overlay_path)
                
                # Create clip
                overlay_clip = (mp.ImageClip(str(temp_overlay_path), duration=self.video_duration)
                              .set_position(overlay_info['position'])
                              .set_start(2))  # Start overlays after 2 seconds
                
                overlay_clips.append(overlay_clip)
            
            # Add title overlay
            title_clip = self._create_title_overlay(story_node, node_id)
            if title_clip:
                overlay_clips.append(title_clip)
            
            # Composite video
            final_clip = mp.CompositeVideoClip([background_clip] + overlay_clips)
            
            # Write video
            final_clip.write_videofile(
                str(output_path),
                fps=24,
                codec='libx264',
                audio_codec='aac' if hasattr(final_clip, 'audio') and final_clip.audio else None,
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Clean up temp files
            for i in range(len(choice_overlays)):
                temp_file = self.videos_dir / f"{node_id}_overlay_{i}.png"
                if temp_file.exists():
                    temp_file.unlink()
            
            # Check file size
            file_size_mb = output_path.stat().st_size / (1024 * 1024)
            if file_size_mb > self.max_file_size_mb:
                logger.warning(f"Video file size ({file_size_mb:.1f}MB) exceeds limit ({self.max_file_size_mb}MB)")
                # Could implement compression here
            
            logger.info(f"Created video: {output_path} ({file_size_mb:.1f}MB)")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error creating video: {e}")
            return None
    
    def _create_title_overlay(self, story_node, node_id: str) -> Optional[mp.VideoClip]:
        """Create title overlay for the video"""
        try:
            # Create title image
            title_width = self.width - 100
            title_height = 100
            
            title_img = Image.new('RGBA', (title_width, title_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(title_img)
            
            # Background
            draw.rounded_rectangle(
                [(0, 0), (title_width, title_height)],
                radius=15,
                fill=(0, 0, 0, 180)
            )
            
            # Title text
            try:
                font = ImageFont.truetype("Arial.ttf", 20)
            except:
                font = ImageFont.load_default()
            
            # Get first sentence of node text
            title_text = story_node.text.split('.')[0][:80]
            if len(story_node.text.split('.')[0]) > 80:
                title_text += "..."
            
            # Add rating indicators
            if story_node.rating == "spicy":
                title_text = f"[Spicy] {title_text}"
            if hasattr(story_node, 'is_premium') and story_node.is_premium:
                title_text = f"[Premium] {title_text}"
            
            # Center text
            text_bbox = draw.textbbox((0, 0), title_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            x = (title_width - text_width) // 2
            y = (title_height - text_height) // 2
            
            # Text with shadow
            draw.text((x + 1, y + 1), title_text, fill='black', font=font)
            draw.text((x, y), title_text, fill='white', font=font)
            
            # Save title image
            title_path = self.videos_dir / f"{node_id}_title.png"
            title_img.save(title_path)
            
            # Create clip
            title_clip = (mp.ImageClip(str(title_path), duration=3)
                         .set_position(('center', 50))
                         .set_start(0))
            
            return title_clip
            
        except Exception as e:
            logger.error(f"Error creating title overlay: {e}")
            return None
    
    def generate_batch_videos(self, story_web, progress_callback: Optional[Callable] = None) -> Dict[str, str]:
        """Generate videos for all nodes in a story web"""
        results = {}
        total_nodes = len(story_web.nodes)
        
        try:
            for i, (node_id, node) in enumerate(story_web.nodes.items()):
                if progress_callback:
                    progress = int((i / total_nodes) * 100)
                    progress_callback(progress, f"Generating video for {node_id}...")
                
                # Get available choices for this node
                choices = story_web.get_available_choices(
                    node_id, 
                    node.inventory_state or {}, 
                    node.class_context or "Adventurer"
                )
                
                video_path = self.generate_video_for_node(node, choices)
                if video_path:
                    results[node_id] = video_path
                else:
                    logger.error(f"Failed to generate video for node: {node_id}")
                
                # Small delay to prevent overwhelming the system
                time.sleep(1)
            
            if progress_callback:
                progress_callback(100, "Batch video generation complete")
            
            logger.info(f"Generated {len(results)} videos out of {total_nodes} nodes")
            return results
            
        except Exception as e:
            logger.error(f"Error in batch video generation: {e}")
            return results
