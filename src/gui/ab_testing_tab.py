"""
A/B Testing Tab - Beautiful GUI for X Ads A/B testing
Create and monitor video variant tests without using edit quota
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QListWidget,
    QFormLayout, QComboBox, QSpinBox, QTextEdit, QLineEdit,
    QFrame, QScrollArea, QGridLayout, QProgressBar, QDoubleSpinBox,
    QFileDialog, QMessageBox, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QIcon, QColor

logger = logging.getLogger(__name__)


class ABTestingTab(QWidget):
    """A/B testing interface for video variants"""
    
    # Signals
    test_created = pyqtSignal(str)  # test_id
    test_started = pyqtSignal(str)  # test_id
    test_completed = pyqtSignal(str, str)  # test_id, winner_variant
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.current_story = None
        self.active_tests = {}
        
        self._setup_ui()
        self._setup_refresh_timer()
        
    def _setup_ui(self):
        """Setup the A/B testing interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Main content with tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create A/B testing tabs
        self._create_setup_tab()
        self._create_active_tests_tab()
        self._create_results_tab()
        self._create_insights_tab()
        
    def _create_header(self) -> QWidget:
        """Create A/B testing header"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🧪 A/B Testing Lab")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Story selector
        self.story_combo = QComboBox()
        self.story_combo.setMinimumWidth(200)
        self.story_combo.currentTextChanged.connect(self._on_story_changed)
        layout.addWidget(QLabel("Story:"))
        layout.addWidget(self.story_combo)
        
        # Test status
        self.test_status = QLabel("No active tests")
        self.test_status.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.test_status)
        
        return header
    
    def _create_setup_tab(self):
        """Create test setup tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Node selection
        node_group = QGroupBox("📍 Select Story Node")
        node_layout = QVBoxLayout(node_group)
        
        # Node selector
        node_selector_layout = QHBoxLayout()
        
        self.node_combo = QComboBox()
        self.node_combo.setMinimumWidth(300)
        self.node_combo.currentTextChanged.connect(self._on_node_selected)
        node_selector_layout.addWidget(QLabel("Node:"))
        node_selector_layout.addWidget(self.node_combo)
        
        self.refresh_nodes_btn = QPushButton("🔄 Refresh")
        self.refresh_nodes_btn.clicked.connect(self._refresh_nodes)
        node_selector_layout.addWidget(self.refresh_nodes_btn)
        
        node_layout.addLayout(node_selector_layout)
        
        # Node info
        self.node_info = QLabel("Select a node to see details")
        self.node_info.setStyleSheet("color: #666; padding: 10px;")
        self.node_info.setWordWrap(True)
        node_layout.addWidget(self.node_info)
        
        layout.addWidget(node_group)
        
        # Video variants
        variants_group = QGroupBox("🎬 Video Variants")
        variants_layout = QVBoxLayout(variants_group)
        
        # Current video
        current_layout = QFormLayout()
        self.current_video_label = QLabel("None selected")
        current_layout.addRow("Current Video:", self.current_video_label)
        variants_layout.addLayout(current_layout)
        
        # Variant list
        self.variants_table = QTableWidget(0, 4)
        self.variants_table.setHorizontalHeaderLabels(["Variant", "Video File", "Description", "Actions"])
        self.variants_table.horizontalHeader().setStretchLastSection(True)
        variants_layout.addWidget(self.variants_table)
        
        # Variant controls
        variant_controls = QHBoxLayout()
        
        self.add_variant_btn = QPushButton("➕ Add Variant")
        self.add_variant_btn.clicked.connect(self._add_variant)
        variant_controls.addWidget(self.add_variant_btn)
        
        self.remove_variant_btn = QPushButton("🗑️ Remove")
        self.remove_variant_btn.clicked.connect(self._remove_variant)
        self.remove_variant_btn.setEnabled(False)
        variant_controls.addWidget(self.remove_variant_btn)
        
        self.preview_variant_btn = QPushButton("👁️ Preview")
        self.preview_variant_btn.clicked.connect(self._preview_variant)
        self.preview_variant_btn.setEnabled(False)
        variant_controls.addWidget(self.preview_variant_btn)
        
        variant_controls.addStretch()
        variants_layout.addLayout(variant_controls)
        
        layout.addWidget(variants_group)
        
        # Test configuration
        config_group = QGroupBox("⚙️ Test Configuration")
        config_layout = QFormLayout(config_group)
        
        # Test duration
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 168)  # 1 hour to 1 week
        self.duration_spin.setValue(24)
        self.duration_spin.setSuffix(" hours")
        config_layout.addRow("Duration:", self.duration_spin)
        
        # Budget per variant
        self.budget_spin = QDoubleSpinBox()
        self.budget_spin.setRange(5.0, 1000.0)
        self.budget_spin.setValue(20.0)
        self.budget_spin.setPrefix("$")
        config_layout.addRow("Budget per Variant:", self.budget_spin)
        
        # Target audience
        self.audience_combo = QComboBox()
        self.audience_combo.addItems([
            "Automatic (Story Audience)", 
            "Broad Audience", 
            "Engaged Followers",
            "Custom Audience"
        ])
        config_layout.addRow("Target Audience:", self.audience_combo)
        
        # Success metric
        self.metric_combo = QComboBox()
        self.metric_combo.addItems([
            "Engagement Rate", 
            "Click-through Rate", 
            "Conversion Rate",
            "Video Completion Rate"
        ])
        config_layout.addRow("Success Metric:", self.metric_combo)
        
        layout.addWidget(config_group)
        
        # Create test button
        create_layout = QHBoxLayout()
        
        self.create_test_btn = QPushButton("🚀 Create A/B Test")
        self.create_test_btn.clicked.connect(self._create_test)
        self.create_test_btn.setEnabled(False)
        create_layout.addWidget(self.create_test_btn)
        
        create_layout.addStretch()
        layout.addLayout(create_layout)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "🧪 Setup")
    
    def _create_active_tests_tab(self):
        """Create active tests monitoring tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Active tests table
        tests_group = QGroupBox("🔄 Active Tests")
        tests_layout = QVBoxLayout(tests_group)
        
        self.active_tests_table = QTableWidget(0, 7)
        self.active_tests_table.setHorizontalHeaderLabels([
            "Test ID", "Node", "Variants", "Status", "Progress", "Leading", "Actions"
        ])
        self.active_tests_table.horizontalHeader().setStretchLastSection(True)
        self.active_tests_table.itemSelectionChanged.connect(self._on_test_selected)
        tests_layout.addWidget(self.active_tests_table)
        
        # Test controls
        test_controls = QHBoxLayout()
        
        self.pause_test_btn = QPushButton("⏸️ Pause Test")
        self.pause_test_btn.clicked.connect(self._pause_test)
        self.pause_test_btn.setEnabled(False)
        test_controls.addWidget(self.pause_test_btn)
        
        self.resume_test_btn = QPushButton("▶️ Resume Test")
        self.resume_test_btn.clicked.connect(self._resume_test)
        self.resume_test_btn.setEnabled(False)
        test_controls.addWidget(self.resume_test_btn)
        
        self.stop_test_btn = QPushButton("⏹️ Stop Test")
        self.stop_test_btn.clicked.connect(self._stop_test)
        self.stop_test_btn.setEnabled(False)
        test_controls.addWidget(self.stop_test_btn)
        
        test_controls.addStretch()
        
        self.refresh_tests_btn = QPushButton("🔄 Refresh")
        self.refresh_tests_btn.clicked.connect(self._refresh_tests)
        test_controls.addWidget(self.refresh_tests_btn)
        
        tests_layout.addLayout(test_controls)
        
        layout.addWidget(tests_group)
        
        # Test details
        details_group = QGroupBox("📊 Test Details")
        details_layout = QVBoxLayout(details_group)
        
        # Test info
        info_layout = QFormLayout()
        
        self.selected_test_label = QLabel("None")
        info_layout.addRow("Selected Test:", self.selected_test_label)
        
        self.test_duration_label = QLabel("N/A")
        info_layout.addRow("Duration:", self.test_duration_label)
        
        self.test_budget_label = QLabel("N/A")
        info_layout.addRow("Total Budget:", self.test_budget_label)
        
        self.test_confidence_label = QLabel("N/A")
        info_layout.addRow("Confidence Level:", self.test_confidence_label)
        
        details_layout.addLayout(info_layout)
        
        # Variant performance
        self.variant_performance_table = QTableWidget(0, 5)
        self.variant_performance_table.setHorizontalHeaderLabels([
            "Variant", "Impressions", "Clicks", "Engagement", "Cost"
        ])
        self.variant_performance_table.setMaximumHeight(150)
        details_layout.addWidget(QLabel("Variant Performance:"))
        details_layout.addWidget(self.variant_performance_table)
        
        layout.addWidget(details_group)
        
        self.tab_widget.addTab(tab, "🔄 Active Tests")
    
    def _create_results_tab(self):
        """Create test results tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Completed tests
        completed_group = QGroupBox("✅ Completed Tests")
        completed_layout = QVBoxLayout(completed_group)
        
        self.completed_tests_table = QTableWidget(0, 6)
        self.completed_tests_table.setHorizontalHeaderLabels([
            "Test ID", "Node", "Winner", "Confidence", "Improvement", "Applied"
        ])
        self.completed_tests_table.horizontalHeader().setStretchLastSection(True)
        self.completed_tests_table.itemSelectionChanged.connect(self._on_completed_test_selected)
        completed_layout.addWidget(self.completed_tests_table)
        
        layout.addWidget(completed_group)
        
        # Test results details
        results_group = QGroupBox("📈 Test Results")
        results_layout = QVBoxLayout(results_group)
        
        # Results summary
        summary_layout = QFormLayout()
        
        self.winner_variant_label = QLabel("None")
        summary_layout.addRow("Winning Variant:", self.winner_variant_label)
        
        self.improvement_label = QLabel("N/A")
        summary_layout.addRow("Performance Improvement:", self.improvement_label)
        
        self.statistical_significance_label = QLabel("N/A")
        summary_layout.addRow("Statistical Significance:", self.statistical_significance_label)
        
        results_layout.addLayout(summary_layout)
        
        # Apply winner
        apply_layout = QHBoxLayout()
        
        self.apply_winner_btn = QPushButton("✅ Apply Winning Variant")
        self.apply_winner_btn.clicked.connect(self._apply_winner)
        self.apply_winner_btn.setEnabled(False)
        apply_layout.addWidget(self.apply_winner_btn)
        
        self.export_results_btn = QPushButton("📊 Export Results")
        self.export_results_btn.clicked.connect(self._export_results)
        self.export_results_btn.setEnabled(False)
        apply_layout.addWidget(self.export_results_btn)
        
        apply_layout.addStretch()
        results_layout.addLayout(apply_layout)
        
        layout.addWidget(results_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "📈 Results")
    
    def _create_insights_tab(self):
        """Create testing insights tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Testing overview
        overview_group = QGroupBox("📊 Testing Overview")
        overview_layout = QGridLayout(overview_group)
        
        # Metrics cards
        self.total_tests_card = self._create_metric_card("Total Tests", "0", "All time")
        overview_layout.addWidget(self.total_tests_card, 0, 0)
        
        self.active_tests_card = self._create_metric_card("Active Tests", "0", "Currently running")
        overview_layout.addWidget(self.active_tests_card, 0, 1)
        
        self.successful_tests_card = self._create_metric_card("Successful Tests", "0", "With clear winners")
        overview_layout.addWidget(self.successful_tests_card, 0, 2)
        
        self.total_improvement_card = self._create_metric_card("Avg Improvement", "0%", "Performance gain")
        overview_layout.addWidget(self.total_improvement_card, 0, 3)
        
        layout.addWidget(overview_group)
        
        # Insights and recommendations
        insights_group = QGroupBox("💡 AI Insights")
        insights_layout = QVBoxLayout(insights_group)
        
        self.insights_text = QTextEdit()
        self.insights_text.setReadOnly(True)
        self.insights_text.setMaximumHeight(200)
        insights_layout.addWidget(self.insights_text)
        
        # Generate insights button
        insights_controls = QHBoxLayout()
        
        self.generate_insights_btn = QPushButton("🧠 Generate Insights")
        self.generate_insights_btn.clicked.connect(self._generate_insights)
        insights_controls.addWidget(self.generate_insights_btn)
        
        insights_controls.addStretch()
        insights_layout.addLayout(insights_controls)
        
        layout.addWidget(insights_group)
        
        # Best practices
        practices_group = QGroupBox("📚 A/B Testing Best Practices")
        practices_layout = QVBoxLayout(practices_group)
        
        practices_text = QLabel("""
        <b>Tips for Successful A/B Testing:</b><br>
        • Test one variable at a time (video content, thumbnail, etc.)<br>
        • Run tests for at least 24-48 hours for statistical significance<br>
        • Ensure sufficient sample size (minimum 1000 impressions per variant)<br>
        • Don't stop tests early - let them complete for accurate results<br>
        • Document learnings to inform future content creation<br>
        • Test regularly to continuously optimize performance
        """)
        practices_text.setWordWrap(True)
        practices_text.setStyleSheet("padding: 10px; background-color: #f9f9f9; border-radius: 5px;")
        practices_layout.addWidget(practices_text)
        
        layout.addWidget(practices_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "💡 Insights")
    
    def _create_metric_card(self, title: str, value: str, subtitle: str) -> QWidget:
        """Create a metric display card"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(card)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_label.setStyleSheet("color: #666;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet("color: #333;")
        layout.addWidget(value_label)
        
        # Subtitle
        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Arial", 9))
        subtitle_label.setStyleSheet("color: #888;")
        layout.addWidget(subtitle_label)
        
        # Store labels for updates
        card.title_label = title_label
        card.value_label = value_label
        card.subtitle_label = subtitle_label
        
        return card
    
    def _setup_refresh_timer(self):
        """Setup automatic refresh timer for active tests"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_tests)
        self.refresh_timer.start(60000)  # Refresh every minute
    
    def _on_story_changed(self, story_id: str):
        """Handle story selection change"""
        if story_id and story_id != "Select Story...":
            self.current_story = story_id
            self._refresh_nodes()
            self._refresh_tests()
    
    def _on_node_selected(self, node_id: str):
        """Handle node selection"""
        if node_id and node_id != "Select Node...":
            # TODO: Load node information and current video
            self.node_info.setText(f"Node: {node_id}\nCurrent video: example.mp4\nPost ID: 123456789")
            self.current_video_label.setText("example.mp4")
            self._check_create_test_ready()
    
    def _refresh_nodes(self):
        """Refresh available nodes for testing"""
        if not self.current_story:
            return
        
        # TODO: Load actual nodes from story
        sample_nodes = ["node_1", "node_2", "node_3", "node_4"]
        
        self.node_combo.clear()
        self.node_combo.addItem("Select Node...")
        self.node_combo.addItems(sample_nodes)
    
    def _add_variant(self):
        """Add a video variant"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Video Variant", "", 
            "Video files (*.mp4 *.avi *.mov);;All files (*.*)"
        )
        
        if file_path:
            # Add to variants table
            row = self.variants_table.rowCount()
            self.variants_table.insertRow(row)
            
            self.variants_table.setItem(row, 0, QTableWidgetItem(f"Variant {row + 1}"))
            self.variants_table.setItem(row, 1, QTableWidgetItem(file_path.split('/')[-1]))
            self.variants_table.setItem(row, 2, QTableWidgetItem("Video variant"))
            
            # Add remove button
            remove_btn = QPushButton("🗑️")
            remove_btn.clicked.connect(lambda: self._remove_variant_row(row))
            self.variants_table.setCellWidget(row, 3, remove_btn)
            
            self._check_create_test_ready()
    
    def _remove_variant(self):
        """Remove selected variant"""
        current_row = self.variants_table.currentRow()
        if current_row >= 0:
            self.variants_table.removeRow(current_row)
            self._check_create_test_ready()
    
    def _remove_variant_row(self, row: int):
        """Remove specific variant row"""
        self.variants_table.removeRow(row)
        self._check_create_test_ready()
    
    def _preview_variant(self):
        """Preview selected variant"""
        # TODO: Implement variant preview
        pass
    
    def _check_create_test_ready(self):
        """Check if ready to create test"""
        node_selected = self.node_combo.currentText() != "Select Node..."
        has_variants = self.variants_table.rowCount() >= 2
        
        self.create_test_btn.setEnabled(node_selected and has_variants)
    
    def _create_test(self):
        """Create a new A/B test"""
        if not self.current_story:
            return
        
        node_id = self.node_combo.currentText()
        duration = self.duration_spin.value()
        budget = self.budget_spin.value()
        
        # TODO: Implement actual test creation
        test_id = f"test_{node_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        QMessageBox.information(self, "Test Created", f"A/B test {test_id} created successfully!")
        
        self.test_created.emit(test_id)
        self._refresh_tests()
    
    def _on_test_selected(self):
        """Handle test selection in active tests"""
        current_row = self.active_tests_table.currentRow()
        if current_row >= 0:
            test_id = self.active_tests_table.item(current_row, 0).text()
            self.selected_test_label.setText(test_id)
            
            # Enable test controls
            self.pause_test_btn.setEnabled(True)
            self.stop_test_btn.setEnabled(True)
    
    def _pause_test(self):
        """Pause selected test"""
        # TODO: Implement test pausing
        pass
    
    def _resume_test(self):
        """Resume selected test"""
        # TODO: Implement test resuming
        pass
    
    def _stop_test(self):
        """Stop selected test"""
        # TODO: Implement test stopping
        pass
    
    def _refresh_tests(self):
        """Refresh active and completed tests"""
        # TODO: Load actual test data
        # For now, show sample data
        self._update_sample_test_data()
    
    def _update_sample_test_data(self):
        """Update with sample test data"""
        # Update metric cards
        self.total_tests_card.value_label.setText("12")
        self.active_tests_card.value_label.setText("2")
        self.successful_tests_card.value_label.setText("8")
        self.total_improvement_card.value_label.setText("23%")
    
    def _on_completed_test_selected(self):
        """Handle completed test selection"""
        current_row = self.completed_tests_table.currentRow()
        if current_row >= 0:
            # TODO: Load test results
            self.winner_variant_label.setText("Variant B")
            self.improvement_label.setText("+15% engagement")
            self.statistical_significance_label.setText("95% confidence")
            
            self.apply_winner_btn.setEnabled(True)
            self.export_results_btn.setEnabled(True)
    
    def _apply_winner(self):
        """Apply winning variant to original post"""
        # TODO: Implement winner application
        QMessageBox.information(self, "Winner Applied", "Winning variant has been applied to the original post!")
    
    def _export_results(self):
        """Export test results"""
        # TODO: Implement results export
        pass
    
    def _generate_insights(self):
        """Generate AI insights from test data"""
        # TODO: Implement AI insights generation
        sample_insights = """
        Based on your A/B testing data:
        
        • Video variants with close-up character shots perform 23% better
        • Tests run during evening hours show higher engagement
        • Shorter videos (under 30 seconds) have better completion rates
        • Action scenes generate more clicks than dialogue scenes
        
        Recommendations:
        • Focus on character-driven content for future tests
        • Schedule tests during peak engagement hours (6-9 PM)
        • Consider creating shorter, more dynamic video variants
        """
        
        self.insights_text.setPlainText(sample_insights)
    
    def set_story(self, story):
        """Set the current story for A/B testing"""
        if story:
            self.current_story = story.metadata.get('id', 'unknown')
            self._refresh_nodes()
            self._refresh_tests()
    
    def update_story_list(self, stories: List[str]):
        """Update the story selection dropdown"""
        self.story_combo.clear()
        self.story_combo.addItem("Select Story...")
        self.story_combo.addItems(stories)
