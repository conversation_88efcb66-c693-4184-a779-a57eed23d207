"""
Analytics Tab - Beautiful GUI for story performance analytics
Real-time engagement, monetization, and optimization insights
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QProgressBar,
    QFormLayout, QComboBox, QSpinBox, QTextEdit, QSplitter,
    QFrame, QScrollArea, QGridLayout
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

logger = logging.getLogger(__name__)


class AnalyticsTab(QWidget):
    """Analytics dashboard for story performance"""
    
    # Signals
    analytics_updated = pyqtSignal(dict)
    optimization_requested = pyqtSignal(str)  # story_id
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.current_story = None
        self.analytics_data = {}
        
        self._setup_ui()
        self._setup_auto_refresh()
        
    def _setup_ui(self):
        """Setup the analytics interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Main content with tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create analytics tabs
        self._create_overview_tab()
        self._create_paths_tab()
        self._create_monetization_tab()
        self._create_optimization_tab()
        
    def _create_header(self) -> QWidget:
        """Create analytics header with controls"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("📊 Story Analytics")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Story selector
        self.story_combo = QComboBox()
        self.story_combo.setMinimumWidth(200)
        self.story_combo.currentTextChanged.connect(self._on_story_changed)
        layout.addWidget(QLabel("Story:"))
        layout.addWidget(self.story_combo)
        
        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self._refresh_analytics)
        layout.addWidget(self.refresh_btn)
        
        # Auto-refresh toggle
        self.auto_refresh_btn = QPushButton("⏰ Auto-Refresh: ON")
        self.auto_refresh_btn.setCheckable(True)
        self.auto_refresh_btn.setChecked(True)
        self.auto_refresh_btn.clicked.connect(self._toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_btn)
        
        return header
    
    def _create_overview_tab(self):
        """Create overview analytics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Key metrics cards
        metrics_layout = QGridLayout()
        
        # Views card
        self.views_card = self._create_metric_card("👀 Total Views", "0", "Across all posts")
        metrics_layout.addWidget(self.views_card, 0, 0)
        
        # Engagement card
        self.engagement_card = self._create_metric_card("💝 Engagement", "0", "0% rate")
        metrics_layout.addWidget(self.engagement_card, 0, 1)
        
        # Revenue card
        self.revenue_card = self._create_metric_card("💰 Revenue", "$0.00", "$0 RPM")
        metrics_layout.addWidget(self.revenue_card, 0, 2)
        
        # Conversion card
        self.conversion_card = self._create_metric_card("🔄 Conversions", "0", "0% rate")
        metrics_layout.addWidget(self.conversion_card, 0, 3)
        
        layout.addLayout(metrics_layout)
        
        # Performance chart area
        chart_group = QGroupBox("📈 Performance Over Time")
        chart_layout = QVBoxLayout(chart_group)
        
        self.performance_chart = QLabel("Performance chart will be displayed here")
        self.performance_chart.setMinimumHeight(200)
        self.performance_chart.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        self.performance_chart.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(self.performance_chart)
        
        layout.addWidget(chart_group)
        
        # Top performing nodes
        top_nodes_group = QGroupBox("🏆 Top Performing Nodes")
        top_nodes_layout = QVBoxLayout(top_nodes_group)
        
        self.top_nodes_table = QTableWidget(0, 4)
        self.top_nodes_table.setHorizontalHeaderLabels(["Node", "Views", "Engagement", "Revenue"])
        self.top_nodes_table.horizontalHeader().setStretchLastSection(True)
        top_nodes_layout.addWidget(self.top_nodes_table)
        
        layout.addWidget(top_nodes_group)
        
        self.tab_widget.addTab(tab, "Overview")
    
    def _create_paths_tab(self):
        """Create path analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Path controls
        controls_layout = QHBoxLayout()
        
        controls_layout.addWidget(QLabel("Sort by:"))
        self.path_sort_combo = QComboBox()
        self.path_sort_combo.addItems(["Revenue", "Completion Rate", "Engagement", "Users"])
        self.path_sort_combo.currentTextChanged.connect(self._update_paths)
        controls_layout.addWidget(self.path_sort_combo)
        
        controls_layout.addWidget(QLabel("Min Completion:"))
        self.min_completion_spin = QSpinBox()
        self.min_completion_spin.setRange(0, 100)
        self.min_completion_spin.setSuffix("%")
        self.min_completion_spin.valueChanged.connect(self._update_paths)
        controls_layout.addWidget(self.min_completion_spin)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Paths table
        self.paths_table = QTableWidget(0, 6)
        self.paths_table.setHorizontalHeaderLabels([
            "Path", "Nodes", "Users", "Completion", "Engagement", "Revenue"
        ])
        self.paths_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.paths_table)
        
        # Path details
        details_group = QGroupBox("🛤️ Path Details")
        details_layout = QVBoxLayout(details_group)
        
        self.path_details = QTextEdit()
        self.path_details.setMaximumHeight(150)
        self.path_details.setReadOnly(True)
        details_layout.addWidget(self.path_details)
        
        layout.addWidget(details_group)
        
        self.tab_widget.addTab(tab, "User Paths")
    
    def _create_monetization_tab(self):
        """Create monetization analytics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Revenue breakdown
        revenue_layout = QGridLayout()
        
        # Revenue by node type
        node_revenue_group = QGroupBox("💰 Revenue by Node Type")
        node_revenue_layout = QVBoxLayout(node_revenue_group)
        
        self.node_revenue_table = QTableWidget(0, 3)
        self.node_revenue_table.setHorizontalHeaderLabels(["Node Type", "Revenue", "Percentage"])
        node_revenue_layout.addWidget(self.node_revenue_table)
        
        revenue_layout.addWidget(node_revenue_group, 0, 0)
        
        # Subscription metrics
        subscription_group = QGroupBox("🔄 Subscription Metrics")
        subscription_layout = QFormLayout(subscription_group)
        
        self.total_subscribers_label = QLabel("0")
        subscription_layout.addRow("Total Subscribers:", self.total_subscribers_label)
        
        self.conversion_rate_label = QLabel("0%")
        subscription_layout.addRow("Conversion Rate:", self.conversion_rate_label)
        
        self.monthly_revenue_label = QLabel("$0.00")
        subscription_layout.addRow("Monthly Revenue:", self.monthly_revenue_label)
        
        self.churn_rate_label = QLabel("0%")
        subscription_layout.addRow("Churn Rate:", self.churn_rate_label)
        
        revenue_layout.addWidget(subscription_group, 0, 1)
        
        layout.addLayout(revenue_layout)
        
        # Revenue optimization suggestions
        optimization_group = QGroupBox("🚀 Revenue Optimization")
        optimization_layout = QVBoxLayout(optimization_group)
        
        self.revenue_suggestions = QTextEdit()
        self.revenue_suggestions.setReadOnly(True)
        self.revenue_suggestions.setMaximumHeight(150)
        optimization_layout.addWidget(self.revenue_suggestions)
        
        layout.addWidget(optimization_group)
        
        self.tab_widget.addTab(tab, "Monetization")
    
    def _create_optimization_tab(self):
        """Create optimization recommendations tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Optimization status
        status_group = QGroupBox("📊 Optimization Status")
        status_layout = QFormLayout(status_group)
        
        self.optimization_score_label = QLabel("0/100")
        status_layout.addRow("Optimization Score:", self.optimization_score_label)
        
        self.optimization_progress = QProgressBar()
        self.optimization_progress.setRange(0, 100)
        status_layout.addRow("Progress:", self.optimization_progress)
        
        layout.addWidget(status_group)
        
        # Recommendations
        recommendations_group = QGroupBox("💡 AI Recommendations")
        recommendations_layout = QVBoxLayout(recommendations_group)
        
        self.recommendations_table = QTableWidget(0, 4)
        self.recommendations_table.setHorizontalHeaderLabels([
            "Priority", "Type", "Suggestion", "Impact"
        ])
        self.recommendations_table.horizontalHeader().setStretchLastSection(True)
        recommendations_layout.addWidget(self.recommendations_table)
        
        # Action buttons
        actions_layout = QHBoxLayout()
        
        self.apply_safe_btn = QPushButton("✅ Apply Safe Changes")
        self.apply_safe_btn.clicked.connect(self._apply_safe_optimizations)
        actions_layout.addWidget(self.apply_safe_btn)
        
        self.generate_variants_btn = QPushButton("🧪 Generate A/B Test Variants")
        self.generate_variants_btn.clicked.connect(self._generate_ab_variants)
        actions_layout.addWidget(self.generate_variants_btn)
        
        actions_layout.addStretch()
        recommendations_layout.addLayout(actions_layout)
        
        layout.addWidget(recommendations_group)
        
        # Edit tracking
        edits_group = QGroupBox("✏️ Edit Usage (X has 5-edit limit)")
        edits_layout = QFormLayout(edits_group)
        
        self.edits_used_label = QLabel("0/5")
        edits_layout.addRow("Edits Used:", self.edits_used_label)
        
        self.edits_progress = QProgressBar()
        self.edits_progress.setRange(0, 5)
        edits_layout.addRow("Edit Quota:", self.edits_progress)
        
        layout.addWidget(edits_group)
        
        self.tab_widget.addTab(tab, "Optimization")
    
    def _create_metric_card(self, title: str, value: str, subtitle: str) -> QWidget:
        """Create a metric display card"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(card)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_label.setStyleSheet("color: #666;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet("color: #333;")
        layout.addWidget(value_label)
        
        # Subtitle
        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Arial", 9))
        subtitle_label.setStyleSheet("color: #888;")
        layout.addWidget(subtitle_label)
        
        # Store labels for updates
        card.title_label = title_label
        card.value_label = value_label
        card.subtitle_label = subtitle_label
        
        return card
    
    def _setup_auto_refresh(self):
        """Setup automatic refresh timer"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_analytics)
        self.refresh_timer.start(30000)  # Refresh every 30 seconds
    
    def _on_story_changed(self, story_id: str):
        """Handle story selection change"""
        if story_id and story_id != "Select Story...":
            self.current_story = story_id
            self._refresh_analytics()
    
    def _refresh_analytics(self):
        """Refresh analytics data"""
        if not self.current_story:
            return
        
        self.refresh_btn.setText("🔄 Refreshing...")
        self.refresh_btn.setEnabled(False)
        
        # TODO: Implement actual analytics refresh
        # For now, simulate with sample data
        self._update_sample_data()
        
        self.refresh_btn.setText("🔄 Refresh")
        self.refresh_btn.setEnabled(True)
    
    def _update_sample_data(self):
        """Update with sample analytics data"""
        # Update metric cards
        self.views_card.value_label.setText("12,450")
        self.views_card.subtitle_label.setText("Across 15 posts")
        
        self.engagement_card.value_label.setText("1,890")
        self.engagement_card.subtitle_label.setText("15.2% rate")
        
        self.revenue_card.value_label.setText("$234.50")
        self.revenue_card.subtitle_label.setText("$18.84 RPM")
        
        self.conversion_card.value_label.setText("62")
        self.conversion_card.subtitle_label.setText("0.50% rate")
        
        # Update optimization score
        self.optimization_score_label.setText("78/100")
        self.optimization_progress.setValue(78)
        
        # Update edit usage
        self.edits_used_label.setText("2/5")
        self.edits_progress.setValue(2)
    
    def _toggle_auto_refresh(self):
        """Toggle auto-refresh"""
        if self.auto_refresh_btn.isChecked():
            self.refresh_timer.start(30000)
            self.auto_refresh_btn.setText("⏰ Auto-Refresh: ON")
        else:
            self.refresh_timer.stop()
            self.auto_refresh_btn.setText("⏰ Auto-Refresh: OFF")
    
    def _update_paths(self):
        """Update path analysis based on filters"""
        # TODO: Implement path filtering and sorting
        pass
    
    def _apply_safe_optimizations(self):
        """Apply safe optimization recommendations"""
        if self.current_story:
            self.optimization_requested.emit(self.current_story)
    
    def _generate_ab_variants(self):
        """Generate A/B test variants"""
        # TODO: Implement A/B test variant generation
        pass
    
    def set_story(self, story):
        """Set the current story for analytics"""
        if story:
            self.current_story = story.metadata.get('id', 'unknown')
            self._refresh_analytics()
    
    def update_story_list(self, stories: List[str]):
        """Update the story selection dropdown"""
        self.story_combo.clear()
        self.story_combo.addItem("Select Story...")
        self.story_combo.addItems(stories)
