"""
Graph Viewer Tab - Visualizes story structure as a graph
Provides interactive node graph with drag-and-drop editing
"""

import logging
from typing import Dict, Any, Optional

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QGraphicsView, QGraphicsScene, QGraphicsItem,
    QGraphicsEllipseItem, QGraphicsTextItem, QGraphicsLineItem,
    QGraphicsRectItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QRectF, QPointF
from PyQt5.QtGui import QFont, QPen, QBrush, QColor

from ..story.story_web import StoryWeb

logger = logging.getLogger(__name__)


class GraphViewerTab(QWidget):
    """Tab for viewing story structure as a graph"""
    
    node_selected = pyqtSignal(str)  # node_id
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.current_story: Optional[StoryWeb] = None
        
        self._setup_ui()
        
        logger.info("Graph viewer tab initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        header_label = QLabel("Story Graph")
        header_label.setFont(QFont("Arial", 12, QFont.Bold))
        header_layout.addWidget(header_label)
        
        # Controls
        refresh_button = QPushButton("Refresh")
        refresh_button.clicked.connect(self.refresh_graph)
        header_layout.addWidget(refresh_button)
        
        layout_button = QPushButton("Auto Layout")
        layout_button.clicked.connect(self.auto_layout)
        header_layout.addWidget(layout_button)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Graph view
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_view.setDragMode(QGraphicsView.RubberBandDrag)
        layout.addWidget(self.graphics_view)
        
        # Status
        self.status_label = QLabel("No story loaded")
        layout.addWidget(self.status_label)
    
    def set_story(self, story: StoryWeb):
        """Set the current story"""
        self.current_story = story
        self.refresh_graph()
    
    def refresh_graph(self):
        """Refresh the graph display"""
        self.graphics_scene.clear()

        if not self.current_story:
            self.status_label.setText("No story loaded")
            return

        try:
            # Create a simple graph layout
            self._create_simple_graph_layout()

            node_count = len(self.current_story.nodes)
            self.status_label.setText(f"Story with {node_count} nodes")

        except Exception as e:
            logger.error(f"Error refreshing graph: {e}")
            # Fallback to placeholder
            placeholder = QGraphicsTextItem("Graph visualization error - see logs")
            placeholder.setPos(100, 100)
            self.graphics_scene.addItem(placeholder)
            self.status_label.setText("Graph error")

    def _create_simple_graph_layout(self):
        """Create a simple graph layout without networkx"""
        nodes = list(self.current_story.nodes.values())
        if not nodes:
            return

        # Simple grid layout
        cols = max(1, int(len(nodes) ** 0.5))
        rows = (len(nodes) + cols - 1) // cols

        node_width = 120
        node_height = 80
        spacing_x = 150
        spacing_y = 100

        # Create node graphics
        node_graphics = {}

        for i, node in enumerate(nodes):
            row = i // cols
            col = i % cols

            x = col * spacing_x + 50
            y = row * spacing_y + 50

            # Create node rectangle
            rect = QGraphicsRectItem(x, y, node_width, node_height)

            # Color based on node type
            if node.is_entry:
                rect.setBrush(QColor(144, 238, 144))  # Light green
            elif node.is_ending:
                rect.setBrush(QColor(255, 182, 193))  # Light pink
            else:
                rect.setBrush(QColor(173, 216, 230))  # Light blue

            rect.setPen(QPen(QColor(0, 0, 0), 2))
            self.graphics_scene.addItem(rect)

            # Add node text
            text_item = QGraphicsTextItem(node.id[:15] + ("..." if len(node.id) > 15 else ""))
            text_item.setPos(x + 5, y + 5)
            text_item.setTextWidth(node_width - 10)
            self.graphics_scene.addItem(text_item)

            # Store for connection drawing
            node_graphics[node.id] = (x + node_width/2, y + node_height/2)

        # Draw connections
        for node in nodes:
            if node.id in node_graphics:
                start_x, start_y = node_graphics[node.id]

                for choice in node.choices:
                    if choice.target_node_id in node_graphics:
                        end_x, end_y = node_graphics[choice.target_node_id]

                        # Draw arrow
                        line = QGraphicsLineItem(start_x, start_y, end_x, end_y)
                        line.setPen(QPen(QColor(100, 100, 100), 1))
                        self.graphics_scene.addItem(line)

                        # Add arrowhead
                        self._add_arrowhead(line, end_x, end_y, start_x, start_y)

        # Fit view to content
        self.graphics_view.fitInView(self.graphics_scene.itemsBoundingRect(), Qt.KeepAspectRatio)

    def _add_arrowhead(self, line, end_x, end_y, start_x, start_y):
        """Add arrowhead to line"""
        import math

        # Calculate arrow direction
        dx = end_x - start_x
        dy = end_y - start_y
        length = math.sqrt(dx*dx + dy*dy)

        if length == 0:
            return

        # Normalize
        dx /= length
        dy /= length

        # Arrow size
        arrow_length = 10
        arrow_angle = 0.5

        # Calculate arrowhead points
        x1 = end_x - arrow_length * (dx * math.cos(arrow_angle) - dy * math.sin(arrow_angle))
        y1 = end_y - arrow_length * (dy * math.cos(arrow_angle) + dx * math.sin(arrow_angle))

        x2 = end_x - arrow_length * (dx * math.cos(-arrow_angle) - dy * math.sin(-arrow_angle))
        y2 = end_y - arrow_length * (dy * math.cos(-arrow_angle) + dx * math.sin(-arrow_angle))

        # Create arrowhead
        arrow1 = QGraphicsLineItem(end_x, end_y, x1, y1)
        arrow2 = QGraphicsLineItem(end_x, end_y, x2, y2)

        arrow1.setPen(QPen(QColor(100, 100, 100), 1))
        arrow2.setPen(QPen(QColor(100, 100, 100), 1))

        self.graphics_scene.addItem(arrow1)
        self.graphics_scene.addItem(arrow2)
    
    def auto_layout(self):
        """Apply automatic layout to the graph"""
        # TODO: Implement automatic graph layout
        logger.info("Auto layout requested")
    
    def on_node_clicked(self, node_id: str):
        """Handle node click"""
        self.node_selected.emit(node_id)
