"""
X Manager Tab - Interface for managing X (Twitter) posting
Handles video generation, content posting, and social media management
"""

import logging
from typing import Dict, Any, Optional

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QGroupBox, QProgressBar, QListWidget, QCheckBox,
    QSpinBox, QFormLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

from ..story.story_web import StoryWeb

logger = logging.getLogger(__name__)


class XManagerTab(QWidget):
    """Tab for managing X (Twitter) posting"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.current_story: Optional[StoryWeb] = None
        
        self._setup_ui()
        
        logger.info("X manager tab initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("X (Twitter) Manager")
        header_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header_label)
        
        # Story info
        story_group = QGroupBox("Story Information")
        story_layout = QFormLayout(story_group)
        
        self.story_title_label = QLabel("No story loaded")
        story_layout.addRow("Title:", self.story_title_label)
        
        self.node_count_label = QLabel("0")
        story_layout.addRow("Nodes:", self.node_count_label)
        
        self.spicy_count_label = QLabel("0")
        story_layout.addRow("Spicy Nodes:", self.spicy_count_label)
        
        layout.addWidget(story_group)
        
        # Generation controls
        gen_group = QGroupBox("Content Generation")
        gen_layout = QVBoxLayout(gen_group)
        
        gen_buttons_layout = QHBoxLayout()
        
        self.generate_videos_button = QPushButton("Generate Videos")
        self.generate_videos_button.clicked.connect(self.generate_videos)
        self.generate_videos_button.setEnabled(False)
        gen_buttons_layout.addWidget(self.generate_videos_button)
        
        self.generate_audio_button = QPushButton("Generate Audio")
        self.generate_audio_button.clicked.connect(self.generate_audio)
        self.generate_audio_button.setEnabled(False)
        gen_buttons_layout.addWidget(self.generate_audio_button)
        
        gen_layout.addLayout(gen_buttons_layout)
        
        # Progress bar
        self.generation_progress = QProgressBar()
        self.generation_progress.setVisible(False)
        gen_layout.addWidget(self.generation_progress)
        
        layout.addWidget(gen_group)
        
        # Posting controls
        post_group = QGroupBox("X Posting")
        post_layout = QVBoxLayout(post_group)
        
        # Post settings
        settings_layout = QFormLayout()
        
        self.include_spicy_check = QCheckBox()
        self.include_spicy_check.setChecked(True)
        settings_layout.addRow("Include Spicy Content:", self.include_spicy_check)
        
        self.post_delay_spin = QSpinBox()
        self.post_delay_spin.setRange(1, 60)
        self.post_delay_spin.setValue(10)
        self.post_delay_spin.setSuffix(" seconds")
        settings_layout.addRow("Post Delay:", self.post_delay_spin)
        
        post_layout.addLayout(settings_layout)
        
        # Post buttons
        post_buttons_layout = QHBoxLayout()
        
        self.preview_posts_button = QPushButton("Preview Posts")
        self.preview_posts_button.clicked.connect(self.preview_posts)
        self.preview_posts_button.setEnabled(False)
        post_buttons_layout.addWidget(self.preview_posts_button)
        
        self.post_story_button = QPushButton("Post Story")
        self.post_story_button.clicked.connect(self.post_story)
        self.post_story_button.setEnabled(False)
        post_buttons_layout.addWidget(self.post_story_button)
        
        post_layout.addLayout(post_buttons_layout)
        
        layout.addWidget(post_group)
        
        # Status and logs
        status_group = QGroupBox("Status & Logs")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
    
    def set_story(self, story: StoryWeb):
        """Set the current story"""
        self.current_story = story
        self._update_story_info()
        self._update_button_states()
    
    def _update_story_info(self):
        """Update story information display"""
        if not self.current_story:
            self.story_title_label.setText("No story loaded")
            self.node_count_label.setText("0")
            self.spicy_count_label.setText("0")
            return
        
        title = self.current_story.metadata.get("title", "Untitled")
        self.story_title_label.setText(title)
        
        node_count = len(self.current_story.nodes)
        self.node_count_label.setText(str(node_count))
        
        spicy_count = sum(1 for node in self.current_story.nodes.values() 
                         if node.rating == "spicy")
        self.spicy_count_label.setText(str(spicy_count))
    
    def _update_button_states(self):
        """Update button enabled states"""
        has_story = self.current_story is not None
        
        self.generate_videos_button.setEnabled(has_story)
        self.generate_audio_button.setEnabled(has_story)
        self.preview_posts_button.setEnabled(has_story)
        self.post_story_button.setEnabled(has_story)
    
    def generate_videos(self):
        """Generate videos for the story"""
        if not self.current_story:
            return
        
        self._log_status("Video generation coming soon...")
        # TODO: Implement video generation with ComfyUI
    
    def generate_audio(self):
        """Generate audio for the story"""
        if not self.current_story:
            return
        
        self._log_status("Audio generation coming soon...")
        # TODO: Implement audio generation with TTS
    
    def preview_posts(self):
        """Preview X posts for the story"""
        if not self.current_story:
            return
        
        self._log_status("Post preview coming soon...")
        # TODO: Implement post preview
    
    def post_story(self):
        """Post the story to X"""
        if not self.current_story:
            return
        
        self._log_status("X posting coming soon...")
        # TODO: Implement X posting
    
    def _log_status(self, message: str):
        """Log a status message"""
        self.status_text.append(f"[{self._get_timestamp()}] {message}")
        logger.info(f"X Manager: {message}")
    
    def _get_timestamp(self):
        """Get current timestamp string"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
