"""
X Manager Tab - Interface for managing X (Twitter) posting
Handles video generation, content posting, and social media management
"""

import logging
from typing import Dict, Any, Optional
from pathlib import Path

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QGroupBox, QProgressBar, QListWidget, QCheckBox,
    QSpinBox, QFormLayout, QComboBox, QMessageBox, QFileDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread
from PyQt5.QtGui import QFont

from ..story.story_web import StoryWeb
from ..media.video_generator import VideoGenerator
from ..media.audio_generator import AudioGenerator
from ..media.comfyui_client import ComfyUIClient
from ..social.x_poster import XPoster
from ..social.paywall_manager import PaywallManager

logger = logging.getLogger(__name__)


class MediaGenerationWorker(QThread):
    """Worker thread for media generation"""

    progress_updated = pyqtSignal(int, str)
    generation_completed = pyqtSignal(dict)
    generation_failed = pyqtSignal(str)

    def __init__(self, story_web, config, ollama_client, generation_type="both"):
        super().__init__()
        self.story_web = story_web
        self.config = config
        self.ollama_client = ollama_client
        self.generation_type = generation_type

    def run(self):
        """Run media generation"""
        try:
            results = {}

            if self.generation_type in ["video", "both"]:
                self.progress_updated.emit(10, "Initializing video generation...")

                # Initialize ComfyUI client
                comfyui_client = ComfyUIClient()
                video_generator = VideoGenerator(self.config, self.ollama_client, comfyui_client)

                self.progress_updated.emit(20, "Generating videos...")
                video_results = video_generator.generate_batch_videos(
                    self.story_web,
                    lambda p, m: self.progress_updated.emit(20 + int(p * 0.4), m)
                )
                results['videos'] = video_results

            if self.generation_type in ["audio", "both"]:
                self.progress_updated.emit(60, "Generating audio...")

                audio_generator = AudioGenerator(self.config, self.ollama_client)
                audio_results = audio_generator.generate_batch_audio(
                    self.story_web,
                    lambda p, m: self.progress_updated.emit(60 + int(p * 0.3), m)
                )
                results['audio'] = audio_results

            self.progress_updated.emit(100, "Media generation complete")
            self.generation_completed.emit(results)

        except Exception as e:
            self.generation_failed.emit(str(e))


class PostingWorker(QThread):
    """Worker thread for X posting"""

    progress_updated = pyqtSignal(int, str)
    posting_completed = pyqtSignal(dict)
    posting_failed = pyqtSignal(str)

    def __init__(self, story_web, video_paths, config):
        super().__init__()
        self.story_web = story_web
        self.video_paths = video_paths
        self.config = config

    def run(self):
        """Run X posting"""
        try:
            self.progress_updated.emit(10, "Initializing X poster...")

            x_poster = XPoster(self.config)
            if not x_poster.is_available():
                self.posting_failed.emit("X API not available - check credentials")
                return

            self.progress_updated.emit(20, "Posting story to X...")

            results = x_poster.post_story_web(
                self.story_web,
                self.video_paths,
                lambda p, m: self.progress_updated.emit(20 + int(p * 0.7), m)
            )

            self.progress_updated.emit(90, "Updating choice links...")
            x_poster.update_choice_links(self.story_web, results)

            self.progress_updated.emit(100, "Posting complete")
            self.posting_completed.emit(results)

        except Exception as e:
            self.posting_failed.emit(str(e))


class XManagerTab(QWidget):
    """Tab for managing X (Twitter) posting"""

    def __init__(self, config: Dict[str, Any], ollama_client):
        super().__init__()

        self.config = config
        self.ollama_client = ollama_client
        self.current_story: Optional[StoryWeb] = None

        # Media generation results
        self.video_paths = {}
        self.audio_paths = {}

        # Workers
        self.media_worker = None
        self.posting_worker = None

        self._setup_ui()

        logger.info("X manager tab initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)

        # Header
        header_label = QLabel("X (Twitter) Manager")
        header_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header_label)

        # Create tab widget for different sections
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # Setup tabs
        self._setup_story_info_tab()
        self._setup_media_generation_tab()
        self._setup_posting_tab()
        self._setup_analytics_tab()

        # Status area
        self._setup_status_area(layout)

    def _setup_story_info_tab(self):
        """Setup story information tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Story info
        story_group = QGroupBox("Story Information")
        story_layout = QFormLayout(story_group)

        self.story_title_label = QLabel("No story loaded")
        story_layout.addRow("Title:", self.story_title_label)

        self.node_count_label = QLabel("0")
        story_layout.addRow("Nodes:", self.node_count_label)

        self.entry_count_label = QLabel("0")
        story_layout.addRow("Entry Points:", self.entry_count_label)

        self.ending_count_label = QLabel("0")
        story_layout.addRow("Endings:", self.ending_count_label)

        self.spicy_count_label = QLabel("0")
        story_layout.addRow("Spicy Nodes:", self.spicy_count_label)

        self.premium_count_label = QLabel("0")
        story_layout.addRow("Premium Nodes:", self.premium_count_label)

        layout.addWidget(story_group)

        # Content warnings
        warnings_group = QGroupBox("Content Warnings")
        warnings_layout = QVBoxLayout(warnings_group)

        self.warnings_text = QTextEdit()
        self.warnings_text.setMaximumHeight(100)
        self.warnings_text.setReadOnly(True)
        warnings_layout.addWidget(self.warnings_text)

        layout.addWidget(warnings_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "Story Info")

    def _setup_media_generation_tab(self):
        """Setup media generation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Generation controls
        gen_group = QGroupBox("Content Generation")
        gen_layout = QVBoxLayout(gen_group)

        # Generation type selection
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Generate:"))

        self.generation_type_combo = QComboBox()
        self.generation_type_combo.addItems(["Both Video & Audio", "Video Only", "Audio Only"])
        type_layout.addWidget(self.generation_type_combo)
        type_layout.addStretch()

        gen_layout.addLayout(type_layout)

        # Generation buttons
        gen_buttons_layout = QHBoxLayout()

        self.generate_media_button = QPushButton("Generate Media")
        self.generate_media_button.clicked.connect(self.generate_media)
        self.generate_media_button.setEnabled(False)
        gen_buttons_layout.addWidget(self.generate_media_button)

        self.stop_generation_button = QPushButton("Stop")
        self.stop_generation_button.clicked.connect(self.stop_generation)
        self.stop_generation_button.setEnabled(False)
        gen_buttons_layout.addWidget(self.stop_generation_button)

        gen_layout.addLayout(gen_buttons_layout)

        # Progress bar
        self.generation_progress = QProgressBar()
        self.generation_progress.setVisible(False)
        gen_layout.addWidget(self.generation_progress)

        # Progress status
        self.generation_status = QLabel("")
        gen_layout.addWidget(self.generation_status)

        layout.addWidget(gen_group)

        # Generation results
        results_group = QGroupBox("Generation Results")
        results_layout = QVBoxLayout(results_group)

        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["Node ID", "Video", "Audio", "Status"])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        results_layout.addWidget(self.results_table)

        layout.addWidget(results_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "Media Generation")

    def _setup_posting_tab(self):
        """Setup X posting tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Posting controls
        post_group = QGroupBox("X Posting")
        post_layout = QVBoxLayout(post_group)

        # Post settings
        settings_layout = QFormLayout()

        self.include_spicy_check = QCheckBox()
        self.include_spicy_check.setChecked(True)
        settings_layout.addRow("Include Spicy Content:", self.include_spicy_check)

        self.include_premium_check = QCheckBox()
        self.include_premium_check.setChecked(True)
        settings_layout.addRow("Include Premium Content:", self.include_premium_check)

        self.post_delay_spin = QSpinBox()
        self.post_delay_spin.setRange(1, 60)
        self.post_delay_spin.setValue(10)
        self.post_delay_spin.setSuffix(" seconds")
        settings_layout.addRow("Post Delay:", self.post_delay_spin)

        self.video_ads_check = QCheckBox()
        self.video_ads_check.setChecked(False)
        self.video_ads_check.setToolTip("Use video ads for clickable choices (requires X Ads API)")
        settings_layout.addRow("Use Video Ads:", self.video_ads_check)

        post_layout.addLayout(settings_layout)

        # Post buttons
        post_buttons_layout = QHBoxLayout()

        self.preview_posts_button = QPushButton("Preview Posts")
        self.preview_posts_button.clicked.connect(self.preview_posts)
        self.preview_posts_button.setEnabled(False)
        post_buttons_layout.addWidget(self.preview_posts_button)

        self.post_story_button = QPushButton("Post Story")
        self.post_story_button.clicked.connect(self.post_story)
        self.post_story_button.setEnabled(False)
        post_buttons_layout.addWidget(self.post_story_button)

        self.stop_posting_button = QPushButton("Stop Posting")
        self.stop_posting_button.clicked.connect(self.stop_posting)
        self.stop_posting_button.setEnabled(False)
        post_buttons_layout.addWidget(self.stop_posting_button)

        post_layout.addLayout(post_buttons_layout)

        # Posting progress
        self.posting_progress = QProgressBar()
        self.posting_progress.setVisible(False)
        post_layout.addWidget(self.posting_progress)

        self.posting_status = QLabel("")
        post_layout.addWidget(self.posting_status)

        layout.addWidget(post_group)

        # Posted content table
        posted_group = QGroupBox("Posted Content")
        posted_layout = QVBoxLayout(posted_group)

        self.posted_table = QTableWidget()
        self.posted_table.setColumnCount(5)
        self.posted_table.setHorizontalHeaderLabels(["Node ID", "Tweet ID", "URL", "Timestamp", "Status"])
        self.posted_table.horizontalHeader().setStretchLastSection(True)
        posted_layout.addWidget(self.posted_table)

        layout.addWidget(posted_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "X Posting")

    def _setup_analytics_tab(self):
        """Setup analytics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Subscriber stats
        subscriber_group = QGroupBox("Subscriber Statistics")
        subscriber_layout = QFormLayout(subscriber_group)

        self.total_subscribers_label = QLabel("0")
        subscriber_layout.addRow("Total Subscribers:", self.total_subscribers_label)

        self.active_subscribers_label = QLabel("0")
        subscriber_layout.addRow("Active Subscribers:", self.active_subscribers_label)

        self.revenue_estimate_label = QLabel("$0.00")
        subscriber_layout.addRow("Monthly Revenue:", self.revenue_estimate_label)

        layout.addWidget(subscriber_group)

        # Content stats
        content_group = QGroupBox("Content Statistics")
        content_layout = QFormLayout(content_group)

        self.total_posts_label = QLabel("0")
        content_layout.addRow("Total Posts:", self.total_posts_label)

        self.spicy_posts_label = QLabel("0")
        content_layout.addRow("Spicy Posts:", self.spicy_posts_label)

        self.premium_posts_label = QLabel("0")
        content_layout.addRow("Premium Posts:", self.premium_posts_label)

        layout.addWidget(content_group)

        # Refresh button
        refresh_button = QPushButton("Refresh Analytics")
        refresh_button.clicked.connect(self.refresh_analytics)
        layout.addWidget(refresh_button)

        layout.addStretch()
        self.tab_widget.addTab(tab, "Analytics")

    def _setup_status_area(self, parent_layout):
        """Setup status area"""
        # Status and logs
        status_group = QGroupBox("Status & Logs")
        status_layout = QVBoxLayout(status_group)

        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)

        parent_layout.addWidget(status_group)
    
    def set_story(self, story: StoryWeb):
        """Set the current story"""
        self.current_story = story
        self._update_story_info()
        self._update_button_states()
        self._clear_results()

    def _update_story_info(self):
        """Update story information display"""
        if not self.current_story:
            self.story_title_label.setText("No story loaded")
            self.node_count_label.setText("0")
            self.entry_count_label.setText("0")
            self.ending_count_label.setText("0")
            self.spicy_count_label.setText("0")
            self.premium_count_label.setText("0")
            self.warnings_text.clear()
            return

        title = self.current_story.metadata.get("title", "Untitled")
        self.story_title_label.setText(title)

        node_count = len(self.current_story.nodes)
        self.node_count_label.setText(str(node_count))

        entry_count = len(self.current_story.entry_points)
        self.entry_count_label.setText(str(entry_count))

        ending_count = len(self.current_story.endings)
        self.ending_count_label.setText(str(ending_count))

        spicy_count = sum(1 for node in self.current_story.nodes.values()
                         if node.rating == "spicy")
        self.spicy_count_label.setText(str(spicy_count))

        premium_count = sum(1 for node in self.current_story.nodes.values()
                           if hasattr(node, 'is_premium') and node.is_premium)
        self.premium_count_label.setText(str(premium_count))

        # Update content warnings
        from ..story.rating_system import RatingSystem
        rating_system = RatingSystem(self.config)
        warnings = rating_system.get_content_warnings(self.current_story)
        self.warnings_text.setPlainText("\n".join(warnings) if warnings else "No content warnings")

    def _update_button_states(self):
        """Update button enabled states"""
        has_story = self.current_story is not None
        has_media = bool(self.video_paths or self.audio_paths)

        self.generate_media_button.setEnabled(has_story)
        self.preview_posts_button.setEnabled(has_story and has_media)
        self.post_story_button.setEnabled(has_story and has_media)

    def _clear_results(self):
        """Clear generation and posting results"""
        self.video_paths.clear()
        self.audio_paths.clear()
        self.results_table.setRowCount(0)
        self.posted_table.setRowCount(0)

    def generate_media(self):
        """Generate media for the story"""
        if not self.current_story:
            return

        # Determine generation type
        generation_type_map = {
            "Both Video & Audio": "both",
            "Video Only": "video",
            "Audio Only": "audio"
        }
        generation_type = generation_type_map[self.generation_type_combo.currentText()]

        # Start generation worker
        self.media_worker = MediaGenerationWorker(
            self.current_story, self.config, self.ollama_client, generation_type
        )

        self.media_worker.progress_updated.connect(self._on_generation_progress)
        self.media_worker.generation_completed.connect(self._on_generation_completed)
        self.media_worker.generation_failed.connect(self._on_generation_failed)

        self.media_worker.start()

        # Update UI
        self.generation_progress.setVisible(True)
        self.generate_media_button.setEnabled(False)
        self.stop_generation_button.setEnabled(True)

        self._log_status(f"Starting {generation_type} generation...")

    def stop_generation(self):
        """Stop media generation"""
        if self.media_worker and self.media_worker.isRunning():
            self.media_worker.terminate()
            self.media_worker.wait()

            self.generation_progress.setVisible(False)
            self.generate_media_button.setEnabled(True)
            self.stop_generation_button.setEnabled(False)

            self._log_status("Media generation stopped")

    def _on_generation_progress(self, progress: int, message: str):
        """Handle generation progress updates"""
        self.generation_progress.setValue(progress)
        self.generation_status.setText(message)

    def _on_generation_completed(self, results: Dict[str, Any]):
        """Handle completed media generation"""
        self.generation_progress.setVisible(False)
        self.generate_media_button.setEnabled(True)
        self.stop_generation_button.setEnabled(False)

        # Store results
        if 'videos' in results:
            self.video_paths.update(results['videos'])
        if 'audio' in results:
            self.audio_paths.update(results['audio'])

        # Update results table
        self._update_results_table()

        # Update button states
        self._update_button_states()

        video_count = len(results.get('videos', {}))
        audio_count = len(results.get('audio', {}))
        self._log_status(f"Media generation complete: {video_count} videos, {audio_count} audio files")

    def _on_generation_failed(self, error: str):
        """Handle failed media generation"""
        self.generation_progress.setVisible(False)
        self.generate_media_button.setEnabled(True)
        self.stop_generation_button.setEnabled(False)

        self._log_status(f"Media generation failed: {error}")
        QMessageBox.critical(self, "Generation Failed", f"Media generation failed:\n{error}")

    def _update_results_table(self):
        """Update the results table"""
        if not self.current_story:
            return

        nodes = list(self.current_story.nodes.keys())
        self.results_table.setRowCount(len(nodes))

        for i, node_id in enumerate(nodes):
            self.results_table.setItem(i, 0, QTableWidgetItem(node_id))

            # Video status
            video_status = "✓" if node_id in self.video_paths else "✗"
            self.results_table.setItem(i, 1, QTableWidgetItem(video_status))

            # Audio status
            audio_status = "✓" if node_id in self.audio_paths else "✗"
            self.results_table.setItem(i, 2, QTableWidgetItem(audio_status))

            # Overall status
            if node_id in self.video_paths and node_id in self.audio_paths:
                status = "Complete"
            elif node_id in self.video_paths or node_id in self.audio_paths:
                status = "Partial"
            else:
                status = "Pending"

            self.results_table.setItem(i, 3, QTableWidgetItem(status))

    def preview_posts(self):
        """Preview X posts for the story"""
        if not self.current_story:
            return

        try:
            # Create preview dialog
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle("Post Preview")
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            preview_text = QTextEdit()
            preview_text.setReadOnly(True)
            layout.addWidget(preview_text)

            # Generate preview content
            preview_content = self._generate_post_preview()
            preview_text.setPlainText(preview_content)

            buttons = QDialogButtonBox(QDialogButtonBox.Ok)
            buttons.accepted.connect(dialog.accept)
            layout.addWidget(buttons)

            dialog.exec_()

        except Exception as e:
            self._log_status(f"Preview failed: {e}")
            QMessageBox.critical(self, "Preview Failed", f"Failed to generate preview:\n{e}")

    def _generate_post_preview(self) -> str:
        """Generate preview of posts"""
        if not self.current_story:
            return "No story loaded"

        preview_lines = []
        preview_lines.append(f"Story: {self.current_story.metadata.get('title', 'Untitled')}")
        preview_lines.append(f"Total nodes: {len(self.current_story.nodes)}")
        preview_lines.append("")

        # Show first few posts
        for i, (node_id, node) in enumerate(self.current_story.nodes.items()):
            if i >= 5:  # Limit preview
                preview_lines.append("... (more posts)")
                break

            preview_lines.append(f"Post {i+1} - Node: {node_id}")
            preview_lines.append(f"Content: {node.text[:100]}...")

            if node.choices:
                preview_lines.append("Choices:")
                for choice in node.choices[:3]:
                    preview_lines.append(f"  → {choice.text}")

            preview_lines.append("")

        return "\n".join(preview_lines)

    def post_story(self):
        """Post the story to X"""
        if not self.current_story:
            return

        # Start posting worker
        self.posting_worker = PostingWorker(
            self.current_story, self.video_paths, self.config
        )

        self.posting_worker.progress_updated.connect(self._on_posting_progress)
        self.posting_worker.posting_completed.connect(self._on_posting_completed)
        self.posting_worker.posting_failed.connect(self._on_posting_failed)

        self.posting_worker.start()

        # Update UI
        self.posting_progress.setVisible(True)
        self.post_story_button.setEnabled(False)
        self.stop_posting_button.setEnabled(True)

        self._log_status("Starting X posting...")

    def stop_posting(self):
        """Stop X posting"""
        if self.posting_worker and self.posting_worker.isRunning():
            self.posting_worker.terminate()
            self.posting_worker.wait()

            self.posting_progress.setVisible(False)
            self.post_story_button.setEnabled(True)
            self.stop_posting_button.setEnabled(False)

            self._log_status("X posting stopped")

    def _on_posting_progress(self, progress: int, message: str):
        """Handle posting progress updates"""
        self.posting_progress.setValue(progress)
        self.posting_status.setText(message)

    def _on_posting_completed(self, results: Dict[str, Any]):
        """Handle completed X posting"""
        self.posting_progress.setVisible(False)
        self.post_story_button.setEnabled(True)
        self.stop_posting_button.setEnabled(False)

        # Update posted table
        self._update_posted_table(results)

        post_count = len(results)
        self._log_status(f"X posting complete: {post_count} posts created")

    def _on_posting_failed(self, error: str):
        """Handle failed X posting"""
        self.posting_progress.setVisible(False)
        self.post_story_button.setEnabled(True)
        self.stop_posting_button.setEnabled(False)

        self._log_status(f"X posting failed: {error}")
        QMessageBox.critical(self, "Posting Failed", f"X posting failed:\n{error}")

    def _update_posted_table(self, results: Dict[str, Any]):
        """Update the posted content table"""
        self.posted_table.setRowCount(len(results))

        for i, (node_id, post_data) in enumerate(results.items()):
            self.posted_table.setItem(i, 0, QTableWidgetItem(node_id))
            self.posted_table.setItem(i, 1, QTableWidgetItem(post_data.get('tweet_id', '')))
            self.posted_table.setItem(i, 2, QTableWidgetItem(post_data.get('url', '')))
            self.posted_table.setItem(i, 3, QTableWidgetItem(post_data.get('timestamp', '')))
            self.posted_table.setItem(i, 4, QTableWidgetItem("Posted"))

    def refresh_analytics(self):
        """Refresh analytics data"""
        try:
            # Initialize paywall manager
            paywall_manager = PaywallManager(self.config)

            # Get subscriber stats
            stats = paywall_manager.get_subscriber_stats()

            self.total_subscribers_label.setText(str(stats.get('total_subscribers', 0)))
            self.active_subscribers_label.setText(str(stats.get('active_subscribers', 0)))
            self.revenue_estimate_label.setText(f"${stats.get('revenue_estimate', 0):.2f}")

            # Update content stats (placeholder)
            self.total_posts_label.setText(str(self.posted_table.rowCount()))

            self._log_status("Analytics refreshed")

        except Exception as e:
            self._log_status(f"Analytics refresh failed: {e}")

    def _log_status(self, message: str):
        """Log a status message"""
        self.status_text.append(f"[{self._get_timestamp()}] {message}")
        logger.info(f"X Manager: {message}")

    def _get_timestamp(self):
        """Get current timestamp string"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
