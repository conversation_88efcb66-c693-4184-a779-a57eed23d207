"""
Authentication Tab - X/Twitter login and subscription management
OAuth 2.0 authentication and rate limiting configuration
"""

import logging
import webbrowser
from typing import Dict, Any, Optional
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QFormLayout, QComboBox, QTextEdit, QLineEdit, QProgressBar,
    QFrame, QGridLayout, QMessageBox, QDialog, QDialogButtonBox,
    QSpinBox, QCheckBox, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer, QUrl
from PyQt5.QtGui import QFont, QPixmap, QDesktopServices

from ..social.x_auth import XAuthenticator, XSubscriptionTier
from ..social.rate_limiter import PostingSchedule

logger = logging.getLogger(__name__)


class AuthenticationTab(QWidget):
    """X authentication and subscription management"""
    
    # Signals
    authentication_changed = pyqtSignal(bool)  # authenticated
    subscription_changed = pyqtSignal(str)  # tier
    rate_limits_updated = pyqtSignal(dict)  # rate limits
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.authenticator = XAuthenticator(config)
        self.rate_limiter = None
        
        self._setup_ui()
        self._update_auth_status()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_status)
        self.refresh_timer.start(60000)  # Refresh every minute
        
    def _setup_ui(self):
        """Setup the authentication interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Main content with tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create auth tabs
        self._create_login_tab()
        self._create_subscription_tab()
        self._create_rate_limits_tab()
        self._create_settings_tab()
        
    def _create_header(self) -> QWidget:
        """Create authentication header"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🔐 X Authentication")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Auth status
        self.auth_status = QLabel("Not authenticated")
        self.auth_status.setStyleSheet("color: #f44336; font-weight: bold;")
        layout.addWidget(self.auth_status)
        
        # User info
        self.user_info = QLabel("")
        self.user_info.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.user_info)
        
        return header
    
    def _create_login_tab(self):
        """Create login/authentication tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Authentication status
        auth_group = QGroupBox("🔐 Authentication Status")
        auth_layout = QVBoxLayout(auth_group)
        
        # Status display
        status_layout = QFormLayout()
        
        self.login_status_label = QLabel("Not logged in")
        status_layout.addRow("Status:", self.login_status_label)
        
        self.username_label = QLabel("N/A")
        status_layout.addRow("Username:", self.username_label)
        
        self.user_id_label = QLabel("N/A")
        status_layout.addRow("User ID:", self.user_id_label)
        
        self.verified_label = QLabel("N/A")
        status_layout.addRow("Verified:", self.verified_label)
        
        auth_layout.addLayout(status_layout)
        
        # Authentication controls
        auth_controls = QHBoxLayout()
        
        self.login_btn = QPushButton("🔑 Login with X")
        self.login_btn.clicked.connect(self._start_login)
        auth_controls.addWidget(self.login_btn)
        
        self.logout_btn = QPushButton("🚪 Logout")
        self.logout_btn.clicked.connect(self._logout)
        self.logout_btn.setEnabled(False)
        auth_controls.addWidget(self.logout_btn)
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self._refresh_auth)
        auth_controls.addWidget(self.refresh_btn)
        
        auth_controls.addStretch()
        auth_layout.addLayout(auth_controls)
        
        layout.addWidget(auth_group)
        
        # OAuth instructions
        instructions_group = QGroupBox("📋 Setup Instructions")
        instructions_layout = QVBoxLayout(instructions_group)
        
        instructions_text = QLabel("""
        <b>To authenticate with X (Twitter):</b><br><br>
        1. Click "Login with X" button below<br>
        2. You'll be redirected to X in your browser<br>
        3. Authorize the application<br>
        4. Copy the authorization code<br>
        5. Paste it in the dialog that appears<br><br>
        
        <b>Required X API Setup:</b><br>
        • X Developer Account<br>
        • OAuth 2.0 App with PKCE<br>
        • Callback URL: http://localhost:8080/callback<br>
        • Scopes: tweet.read, tweet.write, users.read, offline.access
        """)
        instructions_text.setWordWrap(True)
        instructions_text.setStyleSheet("padding: 10px; background-color: #f9f9f9; border-radius: 5px;")
        instructions_layout.addWidget(instructions_text)
        
        layout.addWidget(instructions_group)
        
        # Manual auth code entry
        manual_group = QGroupBox("🔧 Manual Authentication")
        manual_layout = QVBoxLayout(manual_group)
        
        manual_form = QFormLayout()
        
        self.auth_code_input = QLineEdit()
        self.auth_code_input.setPlaceholderText("Enter authorization code from X...")
        manual_form.addRow("Auth Code:", self.auth_code_input)
        
        self.state_input = QLineEdit()
        self.state_input.setPlaceholderText("Enter state parameter...")
        manual_form.addRow("State:", self.state_input)
        
        manual_layout.addLayout(manual_form)
        
        manual_controls = QHBoxLayout()
        
        self.complete_auth_btn = QPushButton("✅ Complete Authentication")
        self.complete_auth_btn.clicked.connect(self._complete_manual_auth)
        manual_controls.addWidget(self.complete_auth_btn)
        
        manual_controls.addStretch()
        manual_layout.addLayout(manual_controls)
        
        layout.addWidget(manual_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "🔑 Login")
    
    def _create_subscription_tab(self):
        """Create subscription management tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Current subscription
        current_group = QGroupBox("📊 Current Subscription")
        current_layout = QFormLayout(current_group)
        
        self.current_tier_label = QLabel("Free")
        current_layout.addRow("Tier:", self.current_tier_label)
        
        self.monthly_cost_label = QLabel("$0")
        current_layout.addRow("Monthly Cost:", self.monthly_cost_label)
        
        self.posts_per_day_label = QLabel("300")
        current_layout.addRow("Posts per Day:", self.posts_per_day_label)
        
        self.posts_per_hour_label = QLabel("50")
        current_layout.addRow("Posts per Hour:", self.posts_per_hour_label)
        
        self.video_length_label = QLabel("140 seconds")
        current_layout.addRow("Max Video Length:", self.video_length_label)
        
        layout.addWidget(current_group)
        
        # Tier selection
        selection_group = QGroupBox("⚙️ Subscription Settings")
        selection_layout = QVBoxLayout(selection_group)
        
        # Auto-detection vs manual
        detection_layout = QHBoxLayout()
        
        self.auto_detect_checkbox = QCheckBox("Auto-detect from X account")
        self.auto_detect_checkbox.setChecked(True)
        self.auto_detect_checkbox.toggled.connect(self._toggle_auto_detect)
        detection_layout.addWidget(self.auto_detect_checkbox)
        
        detection_layout.addStretch()
        selection_layout.addLayout(detection_layout)
        
        # Manual tier selection
        manual_selection = QFormLayout()
        
        self.tier_combo = QComboBox()
        self.tier_combo.addItems([
            "Free (300 posts/day)",
            "X Basic ($3/month - 1000 posts/day)",
            "X Premium ($8/month - 2500 posts/day)",
            "X Premium+ ($16/month - 10000 posts/day)"
        ])
        self.tier_combo.currentTextChanged.connect(self._on_tier_changed)
        self.tier_combo.setEnabled(False)
        manual_selection.addRow("Manual Tier:", self.tier_combo)
        
        selection_layout.addLayout(manual_selection)
        
        # Apply button
        apply_layout = QHBoxLayout()
        
        self.apply_tier_btn = QPushButton("✅ Apply Tier Settings")
        self.apply_tier_btn.clicked.connect(self._apply_tier_settings)
        apply_layout.addWidget(self.apply_tier_btn)
        
        apply_layout.addStretch()
        selection_layout.addLayout(apply_layout)
        
        layout.addWidget(selection_group)
        
        # Tier comparison
        comparison_group = QGroupBox("📋 Tier Comparison")
        comparison_layout = QVBoxLayout(comparison_group)
        
        self.tier_comparison_table = self._create_tier_comparison_table()
        comparison_layout.addWidget(self.tier_comparison_table)
        
        layout.addWidget(comparison_group)
        
        self.tab_widget.addTab(tab, "📊 Subscription")
    
    def _create_rate_limits_tab(self):
        """Create rate limits monitoring tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Current usage
        usage_group = QGroupBox("📈 Current Usage")
        usage_layout = QGridLayout(usage_group)
        
        # Usage cards
        self.daily_usage_card = self._create_usage_card("Daily Usage", "0/300", "0%")
        usage_layout.addWidget(self.daily_usage_card, 0, 0)
        
        self.hourly_usage_card = self._create_usage_card("Hourly Usage", "0/50", "0%")
        usage_layout.addWidget(self.hourly_usage_card, 0, 1)
        
        self.api_usage_card = self._create_usage_card("API Calls", "0/100", "0%")
        usage_layout.addWidget(self.api_usage_card, 0, 2)
        
        layout.addWidget(usage_group)
        
        # Rate limit details
        details_group = QGroupBox("📊 Rate Limit Details")
        details_layout = QFormLayout(details_group)
        
        self.next_daily_reset_label = QLabel("N/A")
        details_layout.addRow("Next Daily Reset:", self.next_daily_reset_label)
        
        self.next_hourly_reset_label = QLabel("N/A")
        details_layout.addRow("Next Hourly Reset:", self.next_hourly_reset_label)
        
        self.can_post_label = QLabel("Unknown")
        details_layout.addRow("Can Post Now:", self.can_post_label)
        
        layout.addWidget(details_group)
        
        # Story posting estimates
        estimates_group = QGroupBox("📅 Story Posting Estimates")
        estimates_layout = QVBoxLayout(estimates_group)
        
        estimate_form = QFormLayout()
        
        self.story_nodes_spin = QSpinBox()
        self.story_nodes_spin.setRange(1, 1000)
        self.story_nodes_spin.setValue(20)
        self.story_nodes_spin.valueChanged.connect(self._update_posting_estimate)
        estimate_form.addRow("Story Nodes:", self.story_nodes_spin)
        
        estimates_layout.addLayout(estimate_form)
        
        # Estimate results
        self.estimate_results = QTextEdit()
        self.estimate_results.setMaximumHeight(150)
        self.estimate_results.setReadOnly(True)
        estimates_layout.addWidget(self.estimate_results)
        
        layout.addWidget(estimates_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "⏱️ Rate Limits")
    
    def _create_settings_tab(self):
        """Create authentication settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # API Configuration
        api_group = QGroupBox("🔧 API Configuration")
        api_layout = QFormLayout(api_group)
        
        self.client_id_input = QLineEdit()
        self.client_id_input.setPlaceholderText("Your X API Client ID...")
        api_layout.addRow("Client ID:", self.client_id_input)
        
        self.client_secret_input = QLineEdit()
        self.client_secret_input.setEchoMode(QLineEdit.Password)
        self.client_secret_input.setPlaceholderText("Your X API Client Secret...")
        api_layout.addRow("Client Secret:", self.client_secret_input)
        
        self.redirect_uri_input = QLineEdit()
        self.redirect_uri_input.setText("http://localhost:8080/callback")
        api_layout.addRow("Redirect URI:", self.redirect_uri_input)
        
        # Save API settings
        api_controls = QHBoxLayout()
        
        self.save_api_btn = QPushButton("💾 Save API Settings")
        self.save_api_btn.clicked.connect(self._save_api_settings)
        api_controls.addWidget(self.save_api_btn)
        
        api_controls.addStretch()
        api_layout.addRow(api_controls)
        
        layout.addWidget(api_group)
        
        # Advanced settings
        advanced_group = QGroupBox("⚙️ Advanced Settings")
        advanced_layout = QFormLayout(advanced_group)
        
        self.auto_refresh_checkbox = QCheckBox("Auto-refresh authentication")
        self.auto_refresh_checkbox.setChecked(True)
        advanced_layout.addRow("Auto Refresh:", self.auto_refresh_checkbox)
        
        self.rate_limit_buffer_spin = QSpinBox()
        self.rate_limit_buffer_spin.setRange(0, 50)
        self.rate_limit_buffer_spin.setValue(10)
        self.rate_limit_buffer_spin.setSuffix("%")
        advanced_layout.addRow("Rate Limit Buffer:", self.rate_limit_buffer_spin)
        
        layout.addWidget(advanced_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "⚙️ Settings")
    
    def _create_tier_comparison_table(self) -> QWidget:
        """Create tier comparison table"""
        # This would create a detailed comparison table
        # For now, return a simple label
        label = QLabel("Tier comparison table would be displayed here")
        label.setStyleSheet("padding: 20px; background-color: #f0f0f0; border: 1px solid #ccc;")
        label.setAlignment(Qt.AlignCenter)
        return label
    
    def _create_usage_card(self, title: str, value: str, percentage: str) -> QWidget:
        """Create a usage display card"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(card)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_label.setStyleSheet("color: #666;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setStyleSheet("color: #333;")
        layout.addWidget(value_label)
        
        # Percentage
        percentage_label = QLabel(percentage)
        percentage_label.setFont(QFont("Arial", 12))
        percentage_label.setStyleSheet("color: #888;")
        layout.addWidget(percentage_label)
        
        # Progress bar
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(0)
        progress.setTextVisible(False)
        layout.addWidget(progress)
        
        # Store references for updates
        card.title_label = title_label
        card.value_label = value_label
        card.percentage_label = percentage_label
        card.progress = progress
        
        return card
    
    def _start_login(self):
        """Start OAuth login process"""
        try:
            auth_url, state = self.authenticator.get_auth_url()
            
            if auth_url:
                # Open browser
                QDesktopServices.openUrl(QUrl(auth_url))
                
                # Show dialog for auth code
                self._show_auth_code_dialog(state)
            else:
                QMessageBox.warning(self, "Error", "Failed to generate authentication URL")
                
        except Exception as e:
            logger.error(f"Error starting login: {e}")
            QMessageBox.critical(self, "Error", f"Login failed: {e}")
    
    def _show_auth_code_dialog(self, state: str):
        """Show dialog to enter authorization code"""
        dialog = QDialog(self)
        dialog.setWindowTitle("X Authentication")
        dialog.setModal(True)
        
        layout = QVBoxLayout(dialog)
        
        # Instructions
        instructions = QLabel("""
        1. Complete authorization in your browser
        2. Copy the authorization code from the callback URL
        3. Paste it below and click OK
        """)
        layout.addWidget(instructions)
        
        # Code input
        code_input = QLineEdit()
        code_input.setPlaceholderText("Authorization code...")
        layout.addWidget(code_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            code = code_input.text().strip()
            if code:
                self._complete_authentication(code, state)
    
    def _complete_authentication(self, code: str, state: str):
        """Complete authentication with code"""
        try:
            success = self.authenticator.authenticate_with_code(code, state)
            
            if success:
                self._update_auth_status()
                self._initialize_rate_limiter()
                QMessageBox.information(self, "Success", "Authentication successful!")
                self.authentication_changed.emit(True)
            else:
                QMessageBox.warning(self, "Error", "Authentication failed")
                
        except Exception as e:
            logger.error(f"Error completing authentication: {e}")
            QMessageBox.critical(self, "Error", f"Authentication failed: {e}")
    
    def _complete_manual_auth(self):
        """Complete manual authentication"""
        code = self.auth_code_input.text().strip()
        state = self.state_input.text().strip()
        
        if code and state:
            self._complete_authentication(code, state)
        else:
            QMessageBox.warning(self, "Error", "Please enter both authorization code and state")
    
    def _logout(self):
        """Logout from X"""
        self.authenticator.logout()
        self._update_auth_status()
        self.authentication_changed.emit(False)
        QMessageBox.information(self, "Logged Out", "Successfully logged out from X")
    
    def _refresh_auth(self):
        """Refresh authentication status"""
        if self.authenticator.refresh_access_token():
            self._update_auth_status()
            QMessageBox.information(self, "Refreshed", "Authentication refreshed successfully")
        else:
            QMessageBox.warning(self, "Error", "Failed to refresh authentication")
    
    def _update_auth_status(self):
        """Update authentication status display"""
        if self.authenticator.is_authenticated():
            self.auth_status.setText("✅ Authenticated")
            self.auth_status.setStyleSheet("color: #4CAF50; font-weight: bold;")
            
            user_info = self.authenticator.user_info
            if user_info:
                username = user_info.get('username', 'Unknown')
                self.user_info.setText(f"@{username}")
                
                # Update login tab
                self.login_status_label.setText("✅ Logged in")
                self.username_label.setText(f"@{username}")
                self.user_id_label.setText(user_info.get('id', 'Unknown'))
                self.verified_label.setText("Yes" if user_info.get('verified') else "No")
                
                self.login_btn.setEnabled(False)
                self.logout_btn.setEnabled(True)
            
            # Update subscription info
            self._update_subscription_info()
            
        else:
            self.auth_status.setText("❌ Not authenticated")
            self.auth_status.setStyleSheet("color: #f44336; font-weight: bold;")
            self.user_info.setText("")
            
            # Update login tab
            self.login_status_label.setText("❌ Not logged in")
            self.username_label.setText("N/A")
            self.user_id_label.setText("N/A")
            self.verified_label.setText("N/A")
            
            self.login_btn.setEnabled(True)
            self.logout_btn.setEnabled(False)
    
    def _update_subscription_info(self):
        """Update subscription information"""
        sub_info = self.authenticator.get_subscription_info()
        tier_data = sub_info['tier_data']
        
        # Update current subscription display
        self.current_tier_label.setText(tier_data['name'])
        self.monthly_cost_label.setText(f"${tier_data['monthly_cost']}")
        self.posts_per_day_label.setText(str(tier_data['posts_per_day']))
        self.posts_per_hour_label.setText(str(tier_data['posts_per_hour']))
        self.video_length_label.setText(f"{tier_data['video_length_max']} seconds")
        
        # Update tier combo
        tier_map = {'free': 0, 'basic': 1, 'premium': 2, 'premium_plus': 3}
        self.tier_combo.setCurrentIndex(tier_map.get(sub_info['tier_id'], 0))
    
    def _toggle_auto_detect(self, enabled: bool):
        """Toggle auto-detection of subscription tier"""
        self.tier_combo.setEnabled(not enabled)
        
        if enabled and self.authenticator.is_authenticated():
            # Re-detect tier
            self.authenticator._detect_subscription_tier()
            self._update_subscription_info()
    
    def _on_tier_changed(self, tier_text: str):
        """Handle manual tier selection"""
        if not self.auto_detect_checkbox.isChecked():
            # Map combo text to tier ID
            tier_map = {
                "Free (300 posts/day)": "free",
                "X Basic ($3/month - 1000 posts/day)": "basic", 
                "X Premium ($8/month - 2500 posts/day)": "premium",
                "X Premium+ ($16/month - 10000 posts/day)": "premium_plus"
            }
            
            tier_id = tier_map.get(tier_text, "free")
            self.authenticator.set_subscription_tier(tier_id)
    
    def _apply_tier_settings(self):
        """Apply tier settings"""
        self._initialize_rate_limiter()
        self._update_rate_limits_display()
        self.subscription_changed.emit(self.authenticator.subscription_tier)
        QMessageBox.information(self, "Applied", "Tier settings applied successfully")
    
    def _initialize_rate_limiter(self):
        """Initialize rate limiter with current settings"""
        rate_limits = self.authenticator.get_rate_limits()
        self.rate_limiter = PostingSchedule(
            self.authenticator.subscription_tier,
            rate_limits
        )
        self.rate_limits_updated.emit(rate_limits)
    
    def _update_rate_limits_display(self):
        """Update rate limits display"""
        if not self.rate_limiter:
            return
        
        stats = self.rate_limiter.get_posting_stats()
        
        # Update usage cards
        daily_usage = stats['current_usage']['posts_today']
        daily_limit = stats['rate_limits']['posts_per_day']
        daily_percent = (daily_usage / daily_limit) * 100 if daily_limit > 0 else 0
        
        self.daily_usage_card.value_label.setText(f"{daily_usage}/{daily_limit}")
        self.daily_usage_card.percentage_label.setText(f"{daily_percent:.1f}%")
        self.daily_usage_card.progress.setValue(int(daily_percent))
        
        hourly_usage = stats['current_usage']['posts_this_hour']
        hourly_limit = stats['rate_limits']['posts_per_hour']
        hourly_percent = (hourly_usage / hourly_limit) * 100 if hourly_limit > 0 else 0
        
        self.hourly_usage_card.value_label.setText(f"{hourly_usage}/{hourly_limit}")
        self.hourly_usage_card.percentage_label.setText(f"{hourly_percent:.1f}%")
        self.hourly_usage_card.progress.setValue(int(hourly_percent))
        
        # Update details
        self.next_daily_reset_label.setText(stats['next_resets']['daily'])
        self.next_hourly_reset_label.setText(stats['next_resets']['hourly'])
        self.can_post_label.setText("Yes" if stats['can_post_now'] else "No")
    
    def _update_posting_estimate(self):
        """Update story posting time estimate"""
        if not self.rate_limiter:
            self.estimate_results.setText("Please authenticate and set subscription tier first")
            return
        
        node_count = self.story_nodes_spin.value()
        estimate = self.rate_limiter.estimate_story_posting_time(node_count)
        
        if estimate['total_time_seconds'] > 0:
            hours = estimate['total_time_hours']
            days = estimate['total_time_days']
            
            result_text = f"""
Story Posting Estimate for {node_count} nodes:

Total Time: {hours:.1f} hours ({days:.1f} days)
Posts per Day: {estimate['posts_per_day']:.1f}
Start Time: {estimate['start_time']}
End Time: {estimate['end_time']}

This estimate assumes optimal posting schedule
respecting your subscription tier rate limits.
            """
        else:
            result_text = "Unable to calculate estimate"
        
        self.estimate_results.setText(result_text.strip())
    
    def _refresh_status(self):
        """Refresh authentication and rate limit status"""
        if self.authenticator.is_authenticated():
            self._update_rate_limits_display()
    
    def _save_api_settings(self):
        """Save API configuration settings"""
        # TODO: Implement API settings saving
        QMessageBox.information(self, "Saved", "API settings saved successfully")
    
    def get_authenticator(self) -> XAuthenticator:
        """Get the authenticator instance"""
        return self.authenticator
    
    def get_rate_limiter(self) -> Optional[PostingSchedule]:
        """Get the rate limiter instance"""
        return self.rate_limiter
