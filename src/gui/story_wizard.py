"""
Story Creation Wizard - Guided story creation for new users
Provides step-by-step guidance from idea to published story
"""

import logging
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (
    QWizard, QWizardPage, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QTextEdit, QComboBox, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QProgressBar, QPushButton, QGroupBox, QFormLayout, QListWidget,
    QListWidgetItem, QMessageBox, QFileDialog, QFrame
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon

from ..story.story_generator import StoryGenerator
from ..story.story_web import StoryWeb
from ..utils.ollama_client import OllamaClient

logger = logging.getLogger(__name__)


class StoryCreationWizard(QWizard):
    """Main story creation wizard"""
    
    story_created = pyqtSignal(object)  # StoryWeb
    
    # Page IDs
    PAGE_WELCOME = 0
    PAGE_STORY_TYPE = 1
    PAGE_BASIC_INFO = 2
    PAGE_SOURCE_CONTENT = 3
    PAGE_CHARACTERS = 4
    PAGE_SETTINGS = 5
    PAGE_GENERATION = 6
    PAGE_COMPLETE = 7
    
    def __init__(self, config: Dict[str, Any], ollama_client: OllamaClient, parent=None):
        super().__init__(parent)
        self.config = config
        self.ollama_client = ollama_client
        self.generated_story = None
        
        self.setWindowTitle("Story Creation Wizard")
        self.setWizardStyle(QWizard.ModernStyle)
        self.setOption(QWizard.HaveHelpButton, True)
        self.setMinimumSize(800, 600)
        
        # Set up pages
        self.setup_pages()
        
        # Connect help button
        self.helpRequested.connect(self.show_help)
    
    def setup_pages(self):
        """Set up all wizard pages"""
        self.setPage(self.PAGE_WELCOME, WelcomePage())
        self.setPage(self.PAGE_STORY_TYPE, StoryTypePage())
        self.setPage(self.PAGE_BASIC_INFO, BasicInfoPage())
        self.setPage(self.PAGE_SOURCE_CONTENT, SourceContentPage())
        self.setPage(self.PAGE_CHARACTERS, CharactersPage())
        self.setPage(self.PAGE_SETTINGS, SettingsPage(self.config))
        self.setPage(self.PAGE_GENERATION, GenerationPage(self.config, self.ollama_client))
        self.setPage(self.PAGE_COMPLETE, CompletePage())
        
        # Connect generation page signal
        generation_page = self.page(self.PAGE_GENERATION)
        generation_page.story_generated.connect(self.on_story_generated)
    
    def show_help(self):
        """Show context-sensitive help"""
        current_page = self.currentPage()
        help_text = getattr(current_page, 'help_text', 'No help available for this page.')
        
        QMessageBox.information(self, "Help", help_text)
    
    def on_story_generated(self, story: StoryWeb):
        """Handle story generation completion"""
        self.generated_story = story
        self.story_created.emit(story)


class WelcomePage(QWizardPage):
    """Welcome page with introduction"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Welcome to Story Creation Wizard")
        self.setSubTitle("Create engaging Choose Your Own Adventure stories for X (Twitter)")
        
        layout = QVBoxLayout()
        
        # Welcome message
        welcome_label = QLabel("""
        <h2>🎭 Welcome to the CYOA Story Creator!</h2>
        
        <p>This wizard will guide you through creating an engaging Choose Your Own Adventure story 
        that can be automatically posted to X (Twitter) with videos and interactive choices.</p>
        
        <h3>What you'll create:</h3>
        <ul>
        <li>📖 <b>Interactive Story</b> - Branching narrative with multiple paths and endings</li>
        <li>🎬 <b>Video Content</b> - AI-generated videos for each story segment</li>
        <li>🎵 <b>Audio Narration</b> - Text-to-speech with character voices</li>
        <li>🐦 <b>X Integration</b> - Automated posting with clickable choices</li>
        <li>💰 <b>Monetization</b> - Premium content and subscription tiers</li>
        </ul>
        
        <h3>What you'll need:</h3>
        <ul>
        <li>✅ A story idea or existing text to adapt</li>
        <li>✅ About 10-15 minutes to complete this wizard</li>
        <li>✅ Ollama running locally (for AI generation)</li>
        <li>⚠️ ComfyUI (optional, for video generation)</li>
        <li>⚠️ X API credentials (optional, for posting)</li>
        </ul>
        
        <p><b>Ready to create your story?</b> Click Next to begin!</p>
        """)
        welcome_label.setWordWrap(True)
        layout.addWidget(welcome_label)
        
        self.setLayout(layout)
        
        self.help_text = """
        This wizard will guide you through creating a complete CYOA story.
        
        The process involves:
        1. Choosing your story type and source
        2. Setting up basic story information
        3. Configuring characters and settings
        4. Generating the story with AI
        5. Reviewing and publishing
        
        You can go back and change settings at any time.
        """


class StoryTypePage(QWizardPage):
    """Page for selecting story type and source"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Story Type & Source")
        self.setSubTitle("How would you like to create your story?")
        
        layout = QVBoxLayout()
        
        # Content type selection
        content_type_group = QGroupBox("Content Type")
        content_type_layout = QVBoxLayout()

        self.content_type_group = QButtonGroup()

        self.story_radio = QRadioButton("Interactive Story (Choose Your Own Adventure)")
        self.story_radio.setChecked(True)
        content_type_layout.addWidget(self.story_radio)
        self.content_type_group.addButton(self.story_radio, 0)

        self.quiz_radio = QRadioButton("Quiz/Personality Test (e.g., 'Which Harry Potter house are you?')")
        content_type_layout.addWidget(self.quiz_radio)
        self.content_type_group.addButton(self.quiz_radio, 1)

        content_type_group.setLayout(content_type_layout)
        layout.addWidget(content_type_group)

        # Story source options
        source_group = QGroupBox("Story Source")
        source_layout = QVBoxLayout()

        self.source_group = QButtonGroup()

        self.import_text_radio = QRadioButton("Import from existing text (book, story, etc.)")
        self.import_text_radio.setChecked(True)
        source_layout.addWidget(self.import_text_radio)
        self.source_group.addButton(self.import_text_radio, 0)

        self.generate_new_radio = QRadioButton("Generate new story/quiz from theme/prompt")
        source_layout.addWidget(self.generate_new_radio)
        self.source_group.addButton(self.generate_new_radio, 1)

        self.template_radio = QRadioButton("Start from story/quiz template")
        source_layout.addWidget(self.template_radio)
        self.source_group.addButton(self.template_radio, 2)

        source_group.setLayout(source_layout)
        layout.addWidget(source_group)
        
        # Story genre
        genre_group = QGroupBox("Story Genre")
        genre_layout = QFormLayout()
        
        self.genre_combo = QComboBox()
        self.genre_combo.addItems([
            "Fantasy Adventure",
            "Sci-Fi Thriller", 
            "Mystery/Detective",
            "Romance",
            "Horror/Supernatural",
            "Historical Fiction",
            "Modern Drama",
            "Comedy/Humor",
            "Action/Adventure",
            "Custom/Other"
        ])
        genre_layout.addRow("Genre:", self.genre_combo)
        
        genre_group.setLayout(genre_layout)
        layout.addWidget(genre_group)
        
        # Target audience
        audience_group = QGroupBox("Target Audience")
        audience_layout = QFormLayout()
        
        self.audience_combo = QComboBox()
        self.audience_combo.addItems([
            "General Audience (Safe for all)",
            "Teen+ (Mild themes)",
            "Adult (Mature themes)",
            "Premium/Spicy (Adult content)"
        ])
        audience_layout.addRow("Audience:", self.audience_combo)
        
        audience_group.setLayout(audience_layout)
        layout.addWidget(audience_group)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # Register fields
        self.registerField("content_type", self.content_type_group, "checkedId")
        self.registerField("story_source", self.source_group, "checkedId")
        self.registerField("story_genre", self.genre_combo, "currentText")
        self.registerField("target_audience", self.audience_combo, "currentText")
        
        self.help_text = """
        Choose how you want to create your story:
        
        • Import from text: Upload an existing story, book chapter, or text that will be converted into an interactive CYOA format
        • Generate new: Create a completely new story from a theme or prompt using AI
        • Template: Start with a pre-built story structure that you can customize
        
        Genre and audience settings help the AI generate appropriate content and determine content ratings.
        """


class BasicInfoPage(QWizardPage):
    """Page for basic story information"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Basic Story Information")
        self.setSubTitle("Tell us about your story")
        
        layout = QFormLayout()
        
        # Story title
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("e.g., The Enchanted Forest Adventure")
        layout.addRow("Story Title*:", self.title_edit)
        
        # Story description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("Brief description of your story (optional)")
        layout.addRow("Description:", self.description_edit)
        
        # Player class
        self.player_class_combo = QComboBox()
        self.player_class_combo.addItems(["Mage", "Ranger", "Charmer"])
        layout.addRow("Player Class:", self.player_class_combo)
        
        # Story length
        length_group = QGroupBox("Story Complexity")
        length_layout = QFormLayout()
        
        self.node_count_spin = QSpinBox()
        self.node_count_spin.setRange(10, 100)
        self.node_count_spin.setValue(25)
        self.node_count_spin.setSuffix(" nodes")
        length_layout.addRow("Target Size:", self.node_count_spin)
        
        self.entry_points_spin = QSpinBox()
        self.entry_points_spin.setRange(1, 5)
        self.entry_points_spin.setValue(3)
        length_layout.addRow("Entry Points:", self.entry_points_spin)
        
        self.min_endings_spin = QSpinBox()
        self.min_endings_spin.setRange(3, 15)
        self.min_endings_spin.setValue(5)
        length_layout.addRow("Minimum Endings:", self.min_endings_spin)
        
        length_group.setLayout(length_layout)
        layout.addRow(length_group)
        
        self.setLayout(layout)
        
        # Register fields
        self.registerField("story_title*", self.title_edit)
        self.registerField("story_description", self.description_edit, "plainText")
        self.registerField("player_class", self.player_class_combo, "currentText")
        self.registerField("node_count", self.node_count_spin)
        self.registerField("entry_points", self.entry_points_spin)
        self.registerField("min_endings", self.min_endings_spin)
        
        self.help_text = """
        Basic story information:
        
        • Title: This will be used for the story and in social media posts
        • Description: Optional summary that helps with AI generation
        • Player Class: Determines available abilities and story perspective
        • Story Size: Larger stories have more paths but take longer to generate
        • Entry Points: Multiple starting points give readers choice
        • Endings: More endings provide variety and replayability
        """


class SourceContentPage(QWizardPage):
    """Page for source content input"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Source Content")
        self.setSubTitle("Provide the source material for your story")
        
        layout = QVBoxLayout()
        
        # File import section
        file_group = QGroupBox("Import from File")
        file_layout = QHBoxLayout()
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("Select a text file to import...")
        file_layout.addWidget(self.file_path_edit)
        
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_file)
        file_layout.addWidget(self.browse_button)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # Text input section
        text_group = QGroupBox("Or Enter Text Directly")
        text_layout = QVBoxLayout()
        
        self.source_text_edit = QTextEdit()
        self.source_text_edit.setPlaceholderText("""
Enter your source text here, or paste from another document.

For best results, include:
• Main characters and their descriptions
• Key plot points and conflicts
• Setting and world details
• Dialogue examples

Example:
"Once upon a time, there was a brave knight named Sir Galahad who lived in the kingdom of Camelot..."
        """.strip())
        text_layout.addWidget(self.source_text_edit)
        
        text_group.setLayout(text_layout)
        layout.addWidget(text_group)
        
        # Theme input (for generated stories)
        theme_group = QGroupBox("Or Provide Theme/Prompt")
        theme_layout = QVBoxLayout()
        
        self.theme_edit = QLineEdit()
        self.theme_edit.setPlaceholderText("e.g., 'A magical academy where students learn forbidden spells'")
        theme_layout.addWidget(self.theme_edit)
        
        theme_group.setLayout(theme_layout)
        layout.addWidget(theme_group)
        
        self.setLayout(layout)
        
        # Register fields
        self.registerField("source_file", self.file_path_edit)
        self.registerField("source_text", self.source_text_edit, "plainText")
        self.registerField("story_theme", self.theme_edit)
        
        self.help_text = """
        Provide source material for your story:
        
        • File Import: Upload a text file (.txt, .md, .docx) containing your story
        • Direct Text: Paste or type your story content directly
        • Theme/Prompt: For AI-generated stories, provide a theme or prompt
        
        The AI will analyze your content to:
        • Extract characters and their traits
        • Identify key plot points
        • Create branching story paths
        • Generate interactive choices
        """
    
    def browse_file(self):
        """Browse for source file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Source File", "", 
            "Text files (*.txt *.md);;Word documents (*.docx);;All files (*.*)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            
            # Try to load file content
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.source_text_edit.setPlainText(content)
            except Exception as e:
                QMessageBox.warning(self, "File Error", f"Could not read file: {e}")
    
    def validatePage(self):
        """Validate that some source content is provided"""
        has_file = bool(self.file_path_edit.text().strip())
        has_text = bool(self.source_text_edit.toPlainText().strip())
        has_theme = bool(self.theme_edit.text().strip())
        
        if not (has_file or has_text or has_theme):
            QMessageBox.warning(
                self, "Missing Content", 
                "Please provide source content by uploading a file, entering text, or providing a theme."
            )
            return False
        
        return True


class CharactersPage(QWizardPage):
    """Page for character configuration"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Character Settings")
        self.setSubTitle("Configure how characters are handled in your story")
        
        layout = QVBoxLayout()
        
        # Character extraction options
        extraction_group = QGroupBox("Character Extraction")
        extraction_layout = QVBoxLayout()
        
        self.auto_extract_check = QCheckBox("Automatically extract characters from source text")
        self.auto_extract_check.setChecked(True)
        extraction_layout.addWidget(self.auto_extract_check)
        
        self.create_default_check = QCheckBox("Create default character set if extraction fails")
        self.create_default_check.setChecked(True)
        extraction_layout.addWidget(self.create_default_check)
        
        extraction_group.setLayout(extraction_layout)
        layout.addWidget(extraction_group)
        
        # Character consistency
        consistency_group = QGroupBox("Character Consistency")
        consistency_layout = QVBoxLayout()
        
        self.track_consistency_check = QCheckBox("Track character consistency across story nodes")
        self.track_consistency_check.setChecked(True)
        consistency_layout.addWidget(self.track_consistency_check)
        
        self.generate_dialogue_check = QCheckBox("Generate character-specific dialogue")
        self.generate_dialogue_check.setChecked(True)
        consistency_layout.addWidget(self.generate_dialogue_check)
        
        consistency_group.setLayout(consistency_layout)
        layout.addWidget(consistency_group)
        
        # Character limits
        limits_group = QGroupBox("Character Limits")
        limits_layout = QFormLayout()
        
        self.max_characters_spin = QSpinBox()
        self.max_characters_spin.setRange(5, 50)
        self.max_characters_spin.setValue(20)
        limits_layout.addRow("Maximum Characters:", self.max_characters_spin)
        
        limits_group.setLayout(limits_layout)
        layout.addWidget(limits_group)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # Register fields
        self.registerField("auto_extract_characters", self.auto_extract_check)
        self.registerField("create_default_characters", self.create_default_check)
        self.registerField("track_consistency", self.track_consistency_check)
        self.registerField("generate_dialogue", self.generate_dialogue_check)
        self.registerField("max_characters", self.max_characters_spin)
        
        self.help_text = """
        Character settings control how the system handles story characters:

        • Auto Extract: AI will identify characters from your source text
        • Default Characters: Creates basic character set (protagonist, antagonist, ally) if extraction fails
        • Track Consistency: Ensures characters appear logically throughout the story
        • Generate Dialogue: Creates character-specific speech patterns and dialogue
        • Character Limits: Prevents too many characters from making the story confusing
        """


class SettingsPage(QWizardPage):
    """Page for generation settings"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.setTitle("Generation Settings")
        self.setSubTitle("Configure how your story will be generated and published")

        layout = QVBoxLayout()

        # Media generation settings
        media_group = QGroupBox("Media Generation")
        media_layout = QFormLayout()

        self.generate_videos_check = QCheckBox("Generate videos for story nodes")
        self.generate_videos_check.setChecked(True)
        media_layout.addRow(self.generate_videos_check)

        self.generate_audio_check = QCheckBox("Generate audio narration")
        self.generate_audio_check.setChecked(True)
        media_layout.addRow(self.generate_audio_check)

        self.video_quality_combo = QComboBox()
        self.video_quality_combo.addItems(["480p", "720p", "1080p"])
        self.video_quality_combo.setCurrentText("720p")
        media_layout.addRow("Video Quality:", self.video_quality_combo)

        media_group.setLayout(media_layout)
        layout.addWidget(media_group)

        # Content settings
        content_group = QGroupBox("Content Settings")
        content_layout = QVBoxLayout()

        self.enable_spicy_check = QCheckBox("Enable spicy/adult content")
        content_layout.addWidget(self.enable_spicy_check)

        self.enable_premium_check = QCheckBox("Mark some content as premium")
        self.enable_premium_check.setChecked(True)
        content_layout.addWidget(self.enable_premium_check)

        self.content_warnings_check = QCheckBox("Generate content warnings")
        self.content_warnings_check.setChecked(True)
        content_layout.addWidget(self.content_warnings_check)

        content_group.setLayout(content_layout)
        layout.addWidget(content_group)

        # Publishing settings
        publish_group = QGroupBox("Publishing Settings")
        publish_layout = QVBoxLayout()

        self.auto_post_check = QCheckBox("Automatically post to X after generation")
        publish_layout.addWidget(self.auto_post_check)

        self.post_delay_spin = QSpinBox()
        self.post_delay_spin.setRange(5, 300)
        self.post_delay_spin.setValue(10)
        self.post_delay_spin.setSuffix(" seconds")
        publish_layout.addWidget(QLabel("Delay between posts:"))
        publish_layout.addWidget(self.post_delay_spin)

        publish_group.setLayout(publish_layout)
        layout.addWidget(publish_group)

        layout.addStretch()
        self.setLayout(layout)

        # Register fields
        self.registerField("generate_videos", self.generate_videos_check)
        self.registerField("generate_audio", self.generate_audio_check)
        self.registerField("video_quality", self.video_quality_combo, "currentText")
        self.registerField("enable_spicy", self.enable_spicy_check)
        self.registerField("enable_premium", self.enable_premium_check)
        self.registerField("content_warnings", self.content_warnings_check)
        self.registerField("auto_post", self.auto_post_check)
        self.registerField("post_delay", self.post_delay_spin)

        self.help_text = """
        Generation and publishing settings:

        • Media Generation: Creates videos and audio for each story segment
        • Video Quality: Higher quality takes longer but looks better
        • Spicy Content: Enables adult themes and content
        • Premium Content: Some nodes require subscription to access
        • Content Warnings: Automatically generates appropriate warnings
        • Auto Post: Publishes to X immediately after generation
        • Post Delay: Time between posts to avoid rate limiting
        """


class GenerationPage(QWizardPage):
    """Page for story generation process"""

    story_generated = pyqtSignal(object)  # StoryWeb

    def __init__(self, config: Dict[str, Any], ollama_client: OllamaClient):
        super().__init__()
        self.config = config
        self.ollama_client = ollama_client
        self.story_generator = None
        self.generated_story = None

        self.setTitle("Generating Your Story")
        self.setSubTitle("Please wait while we create your interactive story...")

        layout = QVBoxLayout()

        # Progress section
        progress_group = QGroupBox("Generation Progress")
        progress_layout = QVBoxLayout()

        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        progress_layout.addWidget(QLabel("Overall Progress:"))
        progress_layout.addWidget(self.overall_progress)

        self.current_step_label = QLabel("Preparing...")
        progress_layout.addWidget(self.current_step_label)

        self.step_progress = QProgressBar()
        self.step_progress.setRange(0, 100)
        progress_layout.addWidget(self.step_progress)

        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)

        # Status log
        log_group = QGroupBox("Generation Log")
        log_layout = QVBoxLayout()

        self.status_log = QTextEdit()
        self.status_log.setMaximumHeight(200)
        self.status_log.setReadOnly(True)
        log_layout.addWidget(self.status_log)

        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Generation")
        self.start_button.clicked.connect(self.start_generation)
        button_layout.addWidget(self.start_button)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.cancel_generation)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        self.setLayout(layout)

        self.help_text = """
        Story generation process:

        1. Analyzing source content
        2. Extracting characters and themes
        3. Creating story structure
        4. Generating story nodes and choices
        5. Creating character profiles
        6. Validating story consistency
        7. Generating media (if enabled)

        This process may take several minutes depending on story size and enabled features.
        """

    def initializePage(self):
        """Initialize the page when entered"""
        self.log_message("Ready to generate story. Click 'Start Generation' to begin.")

    def start_generation(self):
        """Start the story generation process"""
        try:
            self.start_button.setEnabled(False)
            self.cancel_button.setEnabled(True)

            # Get wizard data
            wizard = self.wizard()
            story_data = self.collect_story_data(wizard)

            self.log_message("Starting story generation...")
            self.update_progress(0, "Initializing story generator...")

            # Initialize story generator
            self.story_generator = StoryGenerator(self.config, self.ollama_client)

            # Start generation in thread
            self.generation_thread = StoryGenerationThread(
                self.story_generator, story_data
            )
            self.generation_thread.progress_updated.connect(self.update_progress)
            self.generation_thread.log_message.connect(self.log_message)
            self.generation_thread.generation_completed.connect(self.on_generation_completed)
            self.generation_thread.generation_failed.connect(self.on_generation_failed)

            self.generation_thread.start()

        except Exception as e:
            self.log_message(f"Error starting generation: {e}")
            self.start_button.setEnabled(True)
            self.cancel_button.setEnabled(False)

    def collect_story_data(self, wizard) -> Dict[str, Any]:
        """Collect all data from wizard pages"""
        return {
            'title': wizard.field('story_title'),
            'description': wizard.field('story_description'),
            'genre': wizard.field('story_genre'),
            'audience': wizard.field('target_audience'),
            'player_class': wizard.field('player_class'),
            'source_text': wizard.field('source_text'),
            'theme': wizard.field('story_theme'),
            'node_count': wizard.field('node_count'),
            'entry_points': wizard.field('entry_points'),
            'min_endings': wizard.field('min_endings'),
            'auto_extract_characters': wizard.field('auto_extract_characters'),
            'generate_videos': wizard.field('generate_videos'),
            'generate_audio': wizard.field('generate_audio'),
            'enable_spicy': wizard.field('enable_spicy'),
            'enable_premium': wizard.field('enable_premium')
        }

    def update_progress(self, progress: int, message: str):
        """Update progress display"""
        self.overall_progress.setValue(progress)
        self.current_step_label.setText(message)
        self.log_message(f"[{progress}%] {message}")

    def log_message(self, message: str):
        """Add message to status log"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_log.append(f"[{timestamp}] {message}")

    def on_generation_completed(self, story: StoryWeb):
        """Handle successful generation"""
        self.generated_story = story
        self.update_progress(100, "Story generation completed!")
        self.log_message("✅ Story generation successful!")

        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

        # Enable next button
        self.completeChanged.emit()

        # Emit signal
        self.story_generated.emit(story)

    def on_generation_failed(self, error: str):
        """Handle generation failure"""
        self.log_message(f"❌ Story generation failed: {error}")
        self.update_progress(0, "Generation failed")

        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

    def cancel_generation(self):
        """Cancel the generation process"""
        if hasattr(self, 'generation_thread') and self.generation_thread.isRunning():
            self.generation_thread.terminate()
            self.generation_thread.wait()

        self.log_message("Generation cancelled by user")
        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

    def isComplete(self):
        """Check if page is complete"""
        return self.generated_story is not None


class CompletePage(QWizardPage):
    """Final completion page"""

    def __init__(self):
        super().__init__()
        self.setTitle("Story Creation Complete!")
        self.setSubTitle("Your interactive story has been created successfully")

        layout = QVBoxLayout()

        # Success message
        success_label = QLabel("""
        <h2>🎉 Congratulations!</h2>

        <p>Your Choose Your Own Adventure story has been created successfully!</p>

        <h3>What's been created:</h3>
        <ul>
        <li>📖 Interactive story with multiple paths and endings</li>
        <li>🎭 Character profiles with consistent personalities</li>
        <li>🎬 Video content (if enabled)</li>
        <li>🎵 Audio narration (if enabled)</li>
        <li>💰 Premium content tiers (if enabled)</li>
        </ul>

        <h3>Next steps:</h3>
        <ul>
        <li>✏️ <b>Review and Edit</b> - Use the Story Editor to refine your story</li>
        <li>🎭 <b>Manage Characters</b> - Edit character details in the Characters tab</li>
        <li>📊 <b>View Structure</b> - See your story graph in the Graph Viewer</li>
        <li>🐦 <b>Publish to X</b> - Use the X Manager to post your story</li>
        </ul>

        <p><b>Ready to share your story with the world?</b></p>
        """)
        success_label.setWordWrap(True)
        layout.addWidget(success_label)

        # Story stats (will be populated when story is available)
        self.stats_label = QLabel()
        layout.addWidget(self.stats_label)

        layout.addStretch()
        self.setLayout(layout)

        self.help_text = """
        Your story has been created! You can now:

        • Edit the story content and structure
        • Manage character details and relationships
        • Generate additional media content
        • Post to X (Twitter) for your audience
        • Monitor engagement and analytics

        The story is automatically saved and can be reopened later.
        """

    def initializePage(self):
        """Update stats when page is shown"""
        wizard = self.wizard()
        if hasattr(wizard, 'generated_story') and wizard.generated_story:
            story = wizard.generated_story

            stats_text = f"""
            <h3>Story Statistics:</h3>
            <ul>
            <li>📄 <b>{len(story.nodes)}</b> story nodes created</li>
            <li>🚪 <b>{len(story.entry_points)}</b> entry points</li>
            <li>🏁 <b>{len(story.endings)}</b> possible endings</li>
            <li>🎭 <b>{len(story.get_character_manager().characters) if story.character_manager else 0}</b> characters</li>
            </ul>
            """

            self.stats_label.setText(stats_text)


class StoryGenerationThread(QThread):
    """Thread for story generation to avoid blocking UI"""

    progress_updated = pyqtSignal(int, str)
    log_message = pyqtSignal(str)
    generation_completed = pyqtSignal(object)  # StoryWeb
    generation_failed = pyqtSignal(str)

    def __init__(self, story_generator: StoryGenerator, story_data: Dict[str, Any]):
        super().__init__()
        self.story_generator = story_generator
        self.story_data = story_data

    def run(self):
        """Run story generation"""
        try:
            self.progress_updated.emit(10, "Analyzing source content...")

            # Determine source type and generate story
            if self.story_data.get('source_text'):
                # Import from text
                story = self.story_generator.import_storyline_from_text(
                    self.story_data['source_text'],
                    self.story_data['title'],
                    self.story_data['player_class']
                )
            elif self.story_data.get('theme'):
                # Generate from theme
                self.progress_updated.emit(20, "Generating story from theme...")
                # This would need a new method in story_generator
                story = self.story_generator.generate_from_theme(
                    self.story_data['theme'],
                    self.story_data['title'],
                    self.story_data['player_class']
                )
            else:
                raise ValueError("No source content provided")

            if not story:
                raise ValueError("Story generation failed")

            self.progress_updated.emit(60, "Validating story structure...")

            # Validate story
            is_valid, errors = story.validate_structure()
            if not is_valid:
                self.log_message.emit(f"Story validation issues: {len(errors)} errors")
                for error in errors[:5]:  # Show first 5 errors
                    self.log_message.emit(f"  - {error}")

            self.progress_updated.emit(80, "Finalizing story...")

            # Update story metadata
            story.metadata.update({
                'genre': self.story_data.get('genre', ''),
                'audience': self.story_data.get('audience', ''),
                'description': self.story_data.get('description', ''),
                'wizard_generated': True
            })

            self.progress_updated.emit(100, "Story generation complete!")
            self.generation_completed.emit(story)

        except Exception as e:
            logger.error(f"Story generation failed: {e}")
            self.generation_failed.emit(str(e))
