"""
Main Window - Primary GUI interface for CYOA Automation System
Provides tabbed interface for story management, visualization, and X posting
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional

from PyQt5.QtWidgets import (
    QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout, QWidget,
    QMenuBar, QStatusBar, QAction, QMessageBox, QFileDialog,
    QLabel, QPushButton, QTextEdit, QSplitter, QGroupBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

from .story_editor import StoryEditorTab
from .graph_viewer import GraphViewerTab
from .x_manager import XManagerTab
from .character_editor import CharacterEditorTab
from .story_wizard import StoryCreationWizard
from .analytics_tab import AnalyticsTab
from .quiz_tab import QuizCreationTab
from .voice_tab import VoiceTab
from .ab_testing_tab import ABTestingTab
from .auth_tab import AuthenticationTab
from ..story.story_web import StoryWeb

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """Main application window with tabbed interface"""
    
    # Signals
    status_updated = pyqtSignal(str, str)  # message, type
    story_loaded = pyqtSignal(object)  # StoryWeb
    story_saved = pyqtSignal(str)  # filepath
    
    def __init__(self, config: Dict[str, Any], ollama_client, story_generator,
                 inventory_manager, class_manager, scoring_system, rating_system):
        super().__init__()
        
        self.config = config
        self.ollama_client = ollama_client
        self.story_generator = story_generator
        self.inventory_manager = inventory_manager
        self.class_manager = class_manager
        self.scoring_system = scoring_system
        self.rating_system = rating_system
        
        self.current_story: Optional[StoryWeb] = None
        self.current_file_path: Optional[str] = None
        
        self._setup_ui()
        self._setup_connections()
        self._setup_status_timer()
        
        logger.info("Main window initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("CYOA Automation System")
        self.setGeometry(100, 100, 1400, 900)
        
        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            QTabBar::tab {
                background-color: #555555;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QTextEdit, QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create menu bar
        self._create_menu_bar()
        
        # Create main content area
        self._create_main_content(layout)
        
        # Create status bar
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        wizard_action = QAction('New Story Wizard...', self)
        wizard_action.setShortcut('Ctrl+N')
        wizard_action.triggered.connect(self.show_story_wizard)
        file_menu.addAction(wizard_action)

        new_action = QAction('New Empty Story', self)
        new_action.setShortcut('Ctrl+Shift+N')
        new_action.triggered.connect(self.new_story)
        file_menu.addAction(new_action)
        
        open_action = QAction('Open Story', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_story)
        file_menu.addAction(open_action)
        
        save_action = QAction('Save Story', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_story)
        file_menu.addAction(save_action)
        
        save_as_action = QAction('Save Story As...', self)
        save_as_action.setShortcut('Ctrl+Shift+S')
        save_as_action.triggered.connect(self.save_story_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        import_action = QAction('Import Storyline...', self)
        import_action.triggered.connect(self.import_storyline)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        health_action = QAction('System Health Check', self)
        health_action.triggered.connect(self.show_health_check)
        tools_menu.addAction(health_action)

        tools_menu.addSeparator()

        validate_action = QAction('Validate Story', self)
        validate_action.triggered.connect(self.validate_story)
        tools_menu.addAction(validate_action)

        calculate_scores_action = QAction('Calculate Scores', self)
        calculate_scores_action.triggered.connect(self.calculate_scores)
        tools_menu.addAction(calculate_scores_action)

        update_ratings_action = QAction('Update Ratings', self)
        update_ratings_action.triggered.connect(self.update_ratings)
        tools_menu.addAction(update_ratings_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def _create_main_content(self, layout):
        """Create the main content area with tabs"""
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.story_editor_tab = StoryEditorTab(
            self.config, self.story_generator, self.inventory_manager,
            self.class_manager, self.scoring_system, self.rating_system
        )
        self.tab_widget.addTab(self.story_editor_tab, "Story Editor")
        
        self.graph_viewer_tab = GraphViewerTab(self.config)
        self.tab_widget.addTab(self.graph_viewer_tab, "Graph Viewer")

        self.character_editor_tab = CharacterEditorTab(self.config)
        self.tab_widget.addTab(self.character_editor_tab, "Characters")

        self.x_manager_tab = XManagerTab(self.config, self.ollama_client)
        self.tab_widget.addTab(self.x_manager_tab, "📱 X Manager")

        # Add new feature tabs
        self.analytics_tab = AnalyticsTab(self.config)
        self.tab_widget.addTab(self.analytics_tab, "📊 Analytics")

        self.quiz_tab = QuizCreationTab(self.config)
        self.tab_widget.addTab(self.quiz_tab, "🧠 Quiz Creator")

        self.voice_tab = VoiceTab(self.config)
        self.tab_widget.addTab(self.voice_tab, "🎤 Voice Studio")

        self.ab_testing_tab = ABTestingTab(self.config)
        self.tab_widget.addTab(self.ab_testing_tab, "🧪 A/B Testing")

        # Add authentication tab (first tab for easy access)
        self.auth_tab = AuthenticationTab(self.config)
        self.tab_widget.insertTab(0, self.auth_tab, "🔐 Authentication")

        # Connect tab signals
        self.story_editor_tab.story_changed.connect(self.on_story_changed)
        self.graph_viewer_tab.node_selected.connect(self.on_node_selected)

        # Connect new tab signals
        self.analytics_tab.optimization_requested.connect(self.on_optimization_requested)
        self.quiz_tab.quiz_created.connect(self.on_quiz_created)
        self.voice_tab.voice_sample_added.connect(self.on_voice_sample_added)
        self.ab_testing_tab.test_created.connect(self.on_ab_test_created)

        # Connect authentication signals
        self.auth_tab.authentication_changed.connect(self.on_authentication_changed)
        self.auth_tab.subscription_changed.connect(self.on_subscription_changed)
        self.auth_tab.rate_limits_updated.connect(self.on_rate_limits_updated)
    
    def _create_status_bar(self):
        """Create the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status labels
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        self.ollama_status = QLabel("Ollama: Checking...")
        self.status_bar.addPermanentWidget(self.ollama_status)
        
        self.story_status = QLabel("No story loaded")
        self.status_bar.addPermanentWidget(self.story_status)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.status_updated.connect(self.update_status_display)
        self.story_loaded.connect(self.on_story_loaded)
        self.story_saved.connect(self.on_story_saved)
    
    def _setup_status_timer(self):
        """Setup timer for status updates"""
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_ollama_status)
        self.status_timer.start(10000)  # Update every 10 seconds
        
        # Initial status update
        self.update_ollama_status()
    
    def update_status(self, message: str, status_type: str = "info"):
        """Update status bar message"""
        self.status_updated.emit(message, status_type)
    
    def update_status_display(self, message: str, status_type: str):
        """Update the status display"""
        self.status_label.setText(message)
        
        # Set color based on status type
        if status_type == "error":
            self.status_label.setStyleSheet("color: #ff6b6b;")
        elif status_type == "success":
            self.status_label.setStyleSheet("color: #51cf66;")
        elif status_type == "warning":
            self.status_label.setStyleSheet("color: #ffd43b;")
        else:
            self.status_label.setStyleSheet("color: #ffffff;")
    
    def update_ollama_status(self):
        """Update Ollama connection status"""
        try:
            if self.ollama_client.is_available():
                self.ollama_status.setText("Ollama: Connected")
                self.ollama_status.setStyleSheet("color: #51cf66;")
            else:
                self.ollama_status.setText("Ollama: Disconnected")
                self.ollama_status.setStyleSheet("color: #ff6b6b;")
        except Exception as e:
            self.ollama_status.setText("Ollama: Error")
            self.ollama_status.setStyleSheet("color: #ff6b6b;")
            logger.error(f"Error checking Ollama status: {e}")
    
    def show_story_wizard(self):
        """Show the story creation wizard"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return

            wizard = StoryCreationWizard(self.config, self.ollama_client, self)
            wizard.story_created.connect(self.on_wizard_story_created)

            if wizard.exec_() == wizard.Accepted:
                self.update_status("Story wizard completed", "success")

        except Exception as e:
            logger.error(f"Error showing story wizard: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show story wizard: {e}")

    def on_wizard_story_created(self, story: StoryWeb):
        """Handle story created from wizard"""
        self.current_story = story
        self.current_file_path = None

        self.story_loaded.emit(self.current_story)
        self.update_status("Story created from wizard", "success")

    def show_health_check(self):
        """Show system health check dialog"""
        try:
            from ..utils.system_health import SystemHealthChecker

            health_checker = SystemHealthChecker(self.config)
            health_checker.run_all_checks()

            summary = health_checker.get_health_summary()
            overall_status = summary['overall_status']

            # Create health check dialog
            dialog_text = f"""
            <h3>System Health Check</h3>
            <p><b>Overall Status:</b> {overall_status.upper()}</p>

            <h4>Component Status:</h4>
            <ul>
            """

            for name, check in summary['checks'].items():
                status_icon = "✅" if check['status'] == 'healthy' else "⚠️" if check['status'] == 'warning' else "❌"
                dialog_text += f"<li>{status_icon} <b>{name}:</b> {check['message']}</li>"

            dialog_text += "</ul>"

            if summary['recommendations']:
                dialog_text += "<h4>Recommendations:</h4><ul>"
                for rec in summary['recommendations'][:5]:
                    dialog_text += f"<li>{rec}</li>"
                dialog_text += "</ul>"

            if overall_status == 'healthy':
                QMessageBox.information(self, "System Health", dialog_text)
            elif overall_status == 'warning':
                QMessageBox.warning(self, "System Health", dialog_text)
            else:
                QMessageBox.critical(self, "System Health", dialog_text)

        except Exception as e:
            logger.error(f"Error running health check: {e}")
            QMessageBox.critical(self, "Error", f"Failed to run health check: {e}")

    def new_story(self):
        """Create a new story"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return

            self.current_story = StoryWeb(self.config)
            self.current_file_path = None

            self.story_loaded.emit(self.current_story)
            self.update_status("New story created", "success")

        except Exception as e:
            logger.error(f"Error creating new story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create new story: {e}")
    
    def open_story(self):
        """Open an existing story"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return
            
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Open Story", "data/storylines", "JSON files (*.json)"
            )
            
            if file_path:
                story = StoryWeb.load_from_file(file_path)
                if story:
                    self.current_story = story
                    self.current_file_path = file_path
                    
                    self.story_loaded.emit(self.current_story)
                    self.update_status(f"Opened story: {Path(file_path).name}", "success")
                else:
                    QMessageBox.critical(self, "Error", "Failed to load story file")
                    
        except Exception as e:
            logger.error(f"Error opening story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open story: {e}")
    
    def save_story(self):
        """Save the current story"""
        if not self.current_story:
            return
        
        if self.current_file_path:
            self._save_to_file(self.current_file_path)
        else:
            self.save_story_as()
    
    def save_story_as(self):
        """Save the current story with a new name"""
        if not self.current_story:
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Story As", "data/storylines", "JSON files (*.json)"
        )
        
        if file_path:
            self._save_to_file(file_path)
    
    def _save_to_file(self, file_path: str):
        """Save story to specified file"""
        try:
            if self.current_story.save_to_file(file_path):
                self.current_file_path = file_path
                self.story_saved.emit(file_path)
                self.update_status(f"Saved story: {Path(file_path).name}", "success")
            else:
                QMessageBox.critical(self, "Error", "Failed to save story")
                
        except Exception as e:
            logger.error(f"Error saving story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save story: {e}")
    
    def import_storyline(self):
        """Import a storyline from text"""
        try:
            self.story_editor_tab.import_storyline_dialog()
        except Exception as e:
            logger.error(f"Error importing storyline: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import storyline: {e}")
    
    def validate_story(self):
        """Validate the current story"""
        if not self.current_story:
            QMessageBox.information(self, "Validation", "No story loaded")
            return
        
        try:
            is_valid, errors = self.current_story.validate_structure()
            
            if is_valid:
                QMessageBox.information(self, "Validation", "Story structure is valid!")
            else:
                error_text = "\n".join(errors)
                QMessageBox.warning(self, "Validation Errors", f"Story has validation errors:\n\n{error_text}")
                
        except Exception as e:
            logger.error(f"Error validating story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to validate story: {e}")
    
    def calculate_scores(self):
        """Calculate scores for the current story"""
        if not self.current_story:
            QMessageBox.information(self, "Calculate Scores", "No story loaded")
            return
        
        try:
            self.current_story.calculate_scores()
            self.scoring_system.update_story_scores(self.current_story)
            
            # Refresh displays
            self.story_loaded.emit(self.current_story)
            self.update_status("Scores calculated", "success")
            
        except Exception as e:
            logger.error(f"Error calculating scores: {e}")
            QMessageBox.critical(self, "Error", f"Failed to calculate scores: {e}")
    
    def update_ratings(self):
        """Update content ratings for the current story"""
        if not self.current_story:
            QMessageBox.information(self, "Update Ratings", "No story loaded")
            return
        
        try:
            self.rating_system.update_story_ratings(self.current_story)
            
            # Refresh displays
            self.story_loaded.emit(self.current_story)
            self.update_status("Ratings updated", "success")
            
        except Exception as e:
            logger.error(f"Error updating ratings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to update ratings: {e}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self, 
            "About CYOA Automation System",
            """
            <h3>CYOA Automation System v0.1.0</h3>
            <p>Choose Your Own Adventure automation for X (Twitter)</p>
            <p>Features:</p>
            <ul>
            <li>Local AI story generation with Ollama</li>
            <li>Inventory and class systems</li>
            <li>Video generation with ComfyUI</li>
            <li>Automated X posting</li>
            <li>Content rating and scoring</li>
            </ul>
            <p>Built with PyQt5 and powered by local AI models.</p>
            """
        )
    
    def _check_unsaved_changes(self) -> bool:
        """Check for unsaved changes and prompt user"""
        # For now, always return False (no unsaved changes)
        # TODO: Implement proper change tracking
        return False
    
    def on_story_changed(self, story: StoryWeb):
        """Handle story changes from editor"""
        self.current_story = story

        # Update other tabs
        self.graph_viewer_tab.set_story(story)
        self.character_editor_tab.set_story(story)
        self.x_manager_tab.set_story(story)

        # Update status
        node_count = len(story.nodes) if story else 0
        self.story_status.setText(f"Story: {node_count} nodes")
    
    def on_story_loaded(self, story: StoryWeb):
        """Handle story loaded signal"""
        # Update all tabs
        self.story_editor_tab.set_story(story)
        self.graph_viewer_tab.set_story(story)
        self.character_editor_tab.set_story(story)
        self.x_manager_tab.set_story(story)
        self.analytics_tab.set_story(story)
        self.voice_tab.set_story(story)
        self.ab_testing_tab.set_story(story)

        # Update status
        node_count = len(story.nodes) if story else 0
        self.story_status.setText(f"Story: {node_count} nodes")
    
    def on_story_saved(self, file_path: str):
        """Handle story saved signal"""
        self.setWindowTitle(f"CYOA Automation System - {Path(file_path).name}")
    
    def on_node_selected(self, node_id: str):
        """Handle node selection from graph viewer"""
        if self.current_story and node_id in self.current_story.nodes:
            self.story_editor_tab.select_node(node_id)
    
    def on_optimization_requested(self, story_id: str):
        """Handle optimization request from analytics"""
        try:
            self.update_status("Running story optimization...", "info")
            # TODO: Implement optimization logic
            self.update_status("Optimization completed", "success")
        except Exception as e:
            logger.error(f"Error running optimization: {e}")
            self.update_status("Optimization failed", "error")

    def on_quiz_created(self, quiz):
        """Handle quiz creation"""
        try:
            self.update_status("Quiz created successfully", "success")
            # TODO: Handle quiz integration with story system
        except Exception as e:
            logger.error(f"Error handling quiz creation: {e}")
            self.update_status("Quiz creation failed", "error")

    def on_voice_sample_added(self, character_id: str, sample_path: str):
        """Handle voice sample addition"""
        try:
            self.update_status(f"Voice sample added for {character_id}", "success")
            # TODO: Trigger voice model training if enough samples
        except Exception as e:
            logger.error(f"Error handling voice sample: {e}")
            self.update_status("Voice sample addition failed", "error")

    def on_ab_test_created(self, test_id: str):
        """Handle A/B test creation"""
        try:
            self.update_status(f"A/B test {test_id} created", "success")
            # TODO: Start test monitoring
        except Exception as e:
            logger.error(f"Error handling A/B test creation: {e}")
            self.update_status("A/B test creation failed", "error")

    def on_authentication_changed(self, authenticated: bool):
        """Handle authentication status change"""
        try:
            if authenticated:
                self.update_status("Successfully authenticated with X", "success")
                # Update X manager with authentication
                authenticator = self.auth_tab.get_authenticator()
                if hasattr(self.x_manager_tab, 'set_authenticator'):
                    self.x_manager_tab.set_authenticator(authenticator)
            else:
                self.update_status("Logged out from X", "info")
        except Exception as e:
            logger.error(f"Error handling authentication change: {e}")

    def on_subscription_changed(self, tier: str):
        """Handle subscription tier change"""
        try:
            self.update_status(f"Subscription tier updated to {tier}", "info")
            # Update rate limiting across the application
            rate_limiter = self.auth_tab.get_rate_limiter()
            if rate_limiter and hasattr(self.x_manager_tab, 'set_rate_limiter'):
                self.x_manager_tab.set_rate_limiter(rate_limiter)
        except Exception as e:
            logger.error(f"Error handling subscription change: {e}")

    def on_rate_limits_updated(self, rate_limits: dict):
        """Handle rate limits update"""
        try:
            posts_per_day = rate_limits.get('posts_per_day', 0)
            self.update_status(f"Rate limits updated: {posts_per_day} posts/day", "info")
        except Exception as e:
            logger.error(f"Error handling rate limits update: {e}")

    def closeEvent(self, event):
        """Handle window close event"""
        if self._check_unsaved_changes():
            event.ignore()
            return

        # Save window state
        # TODO: Implement settings persistence

        event.accept()
        logger.info("Main window closed")
