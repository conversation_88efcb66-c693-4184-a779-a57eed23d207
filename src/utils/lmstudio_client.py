"""
LM Studio Client - Interface for LM Studio local LLM server
Replaces Ollama with LM Studio for better model management and UI
"""

import logging
import json
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from pathlib import Path

import requests

logger = logging.getLogger(__name__)


@dataclass
class LMStudioResponse:
    """Response from LM Studio"""
    success: bool
    text: str = ""
    error: str = ""
    model: str = ""
    tokens_used: int = 0
    response_time: float = 0.0
    metadata: Dict[str, Any] = None


class LMStudioClient:
    """Client for LM Studio local LLM server"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.lmstudio_config = self.config.get('lmstudio', {})
        
        # LM Studio connection settings
        self.base_url = self.lmstudio_config.get('base_url', 'http://localhost:1234')
        self.api_key = self.lmstudio_config.get('api_key', 'lm-studio')  # Default for local
        self.timeout = self.lmstudio_config.get('timeout', 120)
        
        # Model settings
        self.default_model = self.lmstudio_config.get('default_model', 'local-model')
        self.max_tokens = self.lmstudio_config.get('max_tokens', 2048)
        self.temperature = self.lmstudio_config.get('temperature', 0.7)
        
        # Model cache
        self.available_models = []
        self.current_model = None
    
    def is_available(self) -> bool:
        """Check if LM Studio is available and running"""
        try:
            response = requests.get(f"{self.base_url}/v1/models", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available models in LM Studio"""
        try:
            response = requests.get(f"{self.base_url}/v1/models", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                models = [model['id'] for model in data.get('data', [])]
                self.available_models = models
                return models
            else:
                logger.error(f"Failed to get models: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            return []
    
    def get_current_model(self) -> Optional[str]:
        """Get currently loaded model"""
        try:
            models = self.get_available_models()
            if models:
                # LM Studio typically has one model loaded at a time
                self.current_model = models[0]
                return self.current_model
            return None
        except Exception as e:
            logger.error(f"Error getting current model: {e}")
            return None
    
    def generate_text(self, prompt: str, model: Optional[str] = None, 
                     max_tokens: Optional[int] = None, 
                     temperature: Optional[float] = None,
                     system_prompt: Optional[str] = None) -> LMStudioResponse:
        """Generate text using LM Studio"""
        start_time = time.time()
        
        try:
            if not self.is_available():
                return LMStudioResponse(
                    success=False,
                    error="LM Studio is not available. Please start LM Studio and load a model."
                )
            
            # Use provided parameters or defaults
            model = model or self.current_model or self.default_model
            max_tokens = max_tokens or self.max_tokens
            temperature = temperature or self.temperature
            
            # Prepare messages
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            # Make request to LM Studio
            payload = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=self.timeout
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract response text
                choices = data.get('choices', [])
                if choices:
                    message = choices[0].get('message', {})
                    text = message.get('content', '').strip()
                    
                    # Extract usage info
                    usage = data.get('usage', {})
                    tokens_used = usage.get('total_tokens', 0)
                    
                    return LMStudioResponse(
                        success=True,
                        text=text,
                        model=model,
                        tokens_used=tokens_used,
                        response_time=response_time,
                        metadata=data
                    )
                else:
                    return LMStudioResponse(
                        success=False,
                        error="No response choices returned",
                        response_time=response_time
                    )
            else:
                error_msg = f"LM Studio API error: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data.get('error', {}).get('message', '')}"
                except:
                    error_msg += f" - {response.text}"
                
                return LMStudioResponse(
                    success=False,
                    error=error_msg,
                    response_time=response_time
                )
                
        except requests.exceptions.Timeout:
            return LMStudioResponse(
                success=False,
                error=f"Request timed out after {self.timeout} seconds",
                response_time=time.time() - start_time
            )
        except Exception as e:
            return LMStudioResponse(
                success=False,
                error=f"Error generating text: {str(e)}",
                response_time=time.time() - start_time
            )
    
    def generate_story_content(self, prompt: str, context: Dict[str, Any] = None) -> LMStudioResponse:
        """Generate story content with optimized prompting"""
        context = context or {}
        
        # Create system prompt for story generation
        system_prompt = """You are a creative storytelling AI that generates engaging, interactive story content. 
        
Your responses should be:
- Vivid and immersive
- Appropriate for the target audience
- Consistent with established characters and world-building
- Engaging and compelling
- Well-structured with clear narrative flow

Focus on creating content that draws readers in and makes them want to continue the story."""
        
        # Add context to the prompt if provided
        if context:
            context_str = "\n".join([f"{k}: {v}" for k, v in context.items() if v])
            full_prompt = f"Context:\n{context_str}\n\nStory Request:\n{prompt}"
        else:
            full_prompt = prompt
        
        return self.generate_text(
            prompt=full_prompt,
            system_prompt=system_prompt,
            temperature=0.8,  # Higher creativity for stories
            max_tokens=1024
        )
    
    def generate_character_dialogue(self, character_name: str, dialogue_prompt: str, 
                                  character_traits: Dict[str, Any] = None) -> LMStudioResponse:
        """Generate character dialogue with personality consistency"""
        character_traits = character_traits or {}
        
        # Create character-specific system prompt
        traits_str = ""
        if character_traits:
            traits_str = f"""
Character Traits:
- Personality: {character_traits.get('personality', 'Unknown')}
- Speaking Style: {character_traits.get('speaking_style', 'Normal')}
- Background: {character_traits.get('background', 'Unknown')}
- Motivations: {character_traits.get('motivations', 'Unknown')}
"""
        
        system_prompt = f"""You are {character_name}, a character in an interactive story.
{traits_str}
Respond in character, maintaining consistency with your established personality and speaking style.
Keep responses natural and engaging, appropriate for dialogue in an interactive story."""
        
        return self.generate_text(
            prompt=dialogue_prompt,
            system_prompt=system_prompt,
            temperature=0.7,
            max_tokens=512
        )
    
    def generate_quiz_content(self, quiz_prompt: str, quiz_type: str = "personality") -> LMStudioResponse:
        """Generate quiz content optimized for viral sharing"""
        
        system_prompt = f"""You are a quiz creation expert specializing in viral {quiz_type} quizzes.

Create engaging quiz content that:
- Is highly shareable on social media
- Has clear, distinct outcomes
- Uses engaging questions that reveal personality traits
- Includes fun facts and interesting results
- Is optimized for maximum engagement

Focus on creating content that people will want to share with friends."""
        
        return self.generate_text(
            prompt=quiz_prompt,
            system_prompt=system_prompt,
            temperature=0.8,
            max_tokens=2048
        )
    
    def test_connection(self) -> Dict[str, Any]:
        """Test connection to LM Studio and return status"""
        try:
            # Check if server is running
            if not self.is_available():
                return {
                    'status': 'error',
                    'message': 'LM Studio server is not running',
                    'suggestions': [
                        'Start LM Studio application',
                        'Load a model in LM Studio',
                        'Check that LM Studio is running on localhost:1234'
                    ]
                }
            
            # Get available models
            models = self.get_available_models()
            if not models:
                return {
                    'status': 'warning',
                    'message': 'LM Studio is running but no models are loaded',
                    'suggestions': [
                        'Load a model in LM Studio',
                        'Check LM Studio model library',
                        'Download a compatible model'
                    ]
                }
            
            # Test generation
            test_response = self.generate_text("Hello, this is a test.")
            if not test_response.success:
                return {
                    'status': 'error',
                    'message': f'Text generation failed: {test_response.error}',
                    'suggestions': [
                        'Check model compatibility',
                        'Restart LM Studio',
                        'Try a different model'
                    ]
                }
            
            return {
                'status': 'success',
                'message': 'LM Studio is working correctly',
                'details': {
                    'available_models': models,
                    'current_model': self.get_current_model(),
                    'response_time': test_response.response_time,
                    'tokens_used': test_response.tokens_used
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Connection test failed: {str(e)}',
                'suggestions': [
                    'Check LM Studio installation',
                    'Verify network connectivity',
                    'Check firewall settings'
                ]
            }
    
    def get_model_info(self, model_name: Optional[str] = None) -> Dict[str, Any]:
        """Get information about a specific model"""
        try:
            model_name = model_name or self.get_current_model()
            if not model_name:
                return {'error': 'No model specified or loaded'}
            
            # LM Studio doesn't provide detailed model info via API
            # Return basic info
            return {
                'name': model_name,
                'status': 'loaded' if model_name in self.get_available_models() else 'not_loaded',
                'base_url': self.base_url,
                'max_tokens': self.max_tokens,
                'temperature': self.temperature
            }
            
        except Exception as e:
            return {'error': f'Failed to get model info: {str(e)}'}
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update client configuration"""
        self.config.update(new_config)
        self.lmstudio_config = self.config.get('lmstudio', {})
        
        # Update connection settings
        self.base_url = self.lmstudio_config.get('base_url', self.base_url)
        self.timeout = self.lmstudio_config.get('timeout', self.timeout)
        self.max_tokens = self.lmstudio_config.get('max_tokens', self.max_tokens)
        self.temperature = self.lmstudio_config.get('temperature', self.temperature)
        
        # Reinitialize client
        self._initialize_client()
        
        logger.info("LM Studio client configuration updated")
