"""
Ollama Client - Interface for local LLM communication
Handles story generation, prompt crafting, and text processing
"""

import json
import requests
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class OllamaResponse:
    """Response from Ollama API"""
    text: str
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = None


class OllamaClient:
    """Client for communicating with local Ollama server"""
    
    def __init__(self, base_url: str = "http://localhost:11434", 
                 model: str = "llama3.1", timeout: int = 60):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.timeout = timeout
        self.session = requests.Session()
        
    def is_available(self) -> bool:
        """Check if Ollama server is available"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama server not available: {e}")
            return False
    
    def generate_text(self, prompt: str, max_tokens: int = 1000, 
                     temperature: float = 0.7, retry_count: int = 3) -> OllamaResponse:
        """Generate text using Ollama"""
        for attempt in range(retry_count):
            try:
                payload = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "num_predict": max_tokens,
                        "temperature": temperature,
                        "top_p": 0.9,
                        "stop": ["</story>", "<END>"]
                    }
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return OllamaResponse(
                        text=result.get("response", "").strip(),
                        success=True,
                        metadata={
                            "model": result.get("model"),
                            "total_duration": result.get("total_duration"),
                            "load_duration": result.get("load_duration")
                        }
                    )
                else:
                    logger.warning(f"Ollama API error (attempt {attempt + 1}): {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error generating text (attempt {attempt + 1}): {e}")
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        
        return OllamaResponse(
            text="The adventure ends. The End.",
            success=False,
            error="Failed to generate text after retries"
        )
    
    def generate_story_node(self, context: str, inventory: Dict[str, int], 
                          player_class: str, node_type: str = "story",
                          spicy_rating: bool = False) -> OllamaResponse:
        """Generate a single story node with choices"""
        
        inventory_text = ", ".join([f"{k}: {v}" for k, v in inventory.items()])
        spicy_context = " Include romantic or intimate elements." if spicy_rating else ""
        
        prompt = f"""
You are writing a Choose Your Own Adventure story node. 

Context: {context}
Player Class: {player_class}
Current Inventory: {inventory_text}
Node Type: {node_type}
{spicy_context}

Write a story segment of 50-100 words that:
1. Continues the narrative naturally
2. Considers the player's class abilities ({player_class})
3. References inventory items when relevant
4. Ends with 2-5 meaningful choices
5. Each choice should be 10-20 words

Format your response as JSON:
{{
    "text": "The story text here...",
    "choices": [
        {{
            "text": "Choice 1 text",
            "inventory_requirements": {{"gold": 50}},
            "inventory_changes": {{"gold": -50, "sword": 1}},
            "class_requirements": ["Mage"],
            "is_premium": false,
            "is_spicy": false
        }}
    ],
    "is_ending": false,
    "ending_type": null,
    "rating": "safe"
}}

Ensure choices reflect class abilities and inventory state.
"""
        
        response = self.generate_text(prompt, max_tokens=800)
        
        if response.success:
            try:
                # Try to parse JSON from response
                json_start = response.text.find('{')
                json_end = response.text.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_text = response.text[json_start:json_end]
                    parsed_data = json.loads(json_text)
                    response.metadata = response.metadata or {}
                    response.metadata['parsed_json'] = parsed_data
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from story node response: {e}")
                response.success = False
                response.error = "Invalid JSON response"
        
        return response
    
    def generate_storyline_from_text(self, source_text: str, title: str,
                                   initial_inventory: Dict[str, int],
                                   player_class: str) -> OllamaResponse:
        """Generate a complete storyline from source text"""
        
        inventory_text = ", ".join([f"{k}: {v}" for k, v in initial_inventory.items()])
        
        prompt = f"""
Convert this story into a Choose Your Own Adventure format:

Title: {title}
Source Text: {source_text}

Player Class: {player_class}
Starting Inventory: {inventory_text}

Create a storyline with:
- 5-15 nodes following the original story arc
- Each node 50-100 words
- 2-5 choices per non-ending node
- At least 2 endings (1 death, 1 success)
- Choices that consider inventory and class abilities
- Inventory changes that make sense (buying, finding, using items)

Format as JSON array of nodes:
[
    {{
        "id": "node_0",
        "text": "Story text...",
        "is_entry": true,
        "is_ending": false,
        "choices": [
            {{
                "id": "choice_0_1",
                "text": "Choice text",
                "target_node_id": "node_1",
                "inventory_requirements": {{}},
                "inventory_changes": {{}},
                "class_requirements": [],
                "is_premium": false,
                "is_spicy": false
            }}
        ],
        "rating": "safe"
    }}
]

Ensure the storyline follows the original narrative while adding meaningful choices.
"""
        
        return self.generate_text(prompt, max_tokens=2000)
    
    def generate_intermediary_nodes(self, storyline1_summary: str, 
                                  storyline2_summary: str,
                                  connection_context: str) -> OllamaResponse:
        """Generate intermediary nodes to connect storylines"""
        
        prompt = f"""
Create intermediary nodes to connect these two storylines:

Storyline 1: {storyline1_summary}
Storyline 2: {storyline2_summary}
Connection Context: {connection_context}

Generate 2-4 intermediary nodes that:
1. Provide smooth narrative transition
2. Offer meaningful choices
3. Can funnel to either storyline
4. Include side mission opportunities
5. Consider inventory and class systems

Format as JSON array of nodes with full choice structures.
Each node should be 50-100 words with 2-5 choices.
"""
        
        return self.generate_text(prompt, max_tokens=1500)
    
    def generate_video_prompt(self, node_text: str, choice_text: str,
                            player_class: str, is_spicy: bool = False) -> OllamaResponse:
        """Generate visual prompt for video generation"""
        
        spicy_context = " Include romantic or sensual elements." if is_spicy else ""
        
        prompt = f"""
Create a visual prompt for AI video generation based on this story content:

Story Text: {node_text}
Choice: {choice_text}
Character Class: {player_class}
{spicy_context}

Generate a detailed visual prompt (50-100 words) that:
1. Describes the scene visually
2. Includes character appearance based on class
3. Captures the mood and atmosphere
4. Specifies camera angle and lighting
5. Mentions any relevant props or environment

Keep it cinematic and engaging. Focus on visual elements only.
"""
        
        return self.generate_text(prompt, max_tokens=200)
    
    def generate_audio_script(self, node_text: str, is_spicy: bool = False) -> OllamaResponse:
        """Generate audio script for TTS"""
        
        prompt = f"""
Convert this story text into a natural-sounding audio script for text-to-speech:

Story Text: {node_text}

Create a script that:
1. Flows naturally when spoken
2. Includes appropriate pauses [PAUSE]
3. Emphasizes dramatic moments [EMPHASIS]
4. Is 30-60 seconds when spoken
5. Maintains the story's tone

Return only the audio script text with timing markers.
"""
        
        return self.generate_text(prompt, max_tokens=300)
    
    def analyze_content_rating(self, text: str) -> OllamaResponse:
        """Analyze content for spicy rating"""
        
        prompt = f"""
Analyze this text for content rating:

Text: {text}

Determine if this content should be rated as "spicy" based on:
- Romantic content
- Intimate situations
- Suggestive themes
- Adult content

Respond with JSON:
{{
    "rating": "safe" or "spicy",
    "confidence": 0.0-1.0,
    "reasons": ["reason1", "reason2"]
}}
"""
        
        return self.generate_text(prompt, max_tokens=200)
