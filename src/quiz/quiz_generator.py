"""
Quiz Generator - AI-powered quiz creation from prompts
Creates engaging quizzes using Ollama for content generation
"""

import json
import uuid
import logging
from typing import Dict, List, Optional, Any

from .quiz_system import QuizWeb, QuizType, QuizOutcome, QuizQuestion, QuizResultType
from ..utils.ollama_client import OllamaClient

logger = logging.getLogger(__name__)


class QuizGenerator:
    """Generates quizzes using AI"""
    
    def __init__(self, config: Dict[str, Any], ollama_client: OllamaClient):
        self.config = config
        self.ollama_client = ollama_client
        self.quiz_config = config.get('quiz_generation', {})
    
    def generate_quiz_from_prompt(self, prompt: str, quiz_type: str = "personality") -> Optional[QuizWeb]:
        """Generate a complete quiz from a text prompt"""
        try:
            logger.info(f"Generating quiz from prompt: {prompt[:100]}...")
            
            # Parse quiz type
            quiz_type_enum = QuizType(quiz_type.lower())
            
            # Generate quiz structure
            quiz_structure = self._generate_quiz_structure(prompt, quiz_type_enum)
            if not quiz_structure:
                return None
            
            # Create quiz web
            quiz = QuizWeb(self.config)
            quiz.quiz_type = quiz_type_enum
            quiz.metadata.update(quiz_structure.get('metadata', {}))
            
            # Add outcomes
            for outcome_data in quiz_structure.get('outcomes', []):
                outcome = self._create_outcome_from_data(outcome_data)
                if outcome:
                    quiz.add_outcome(outcome)
            
            # Add questions
            for question_data in quiz_structure.get('questions', []):
                question = self._create_question_from_data(question_data)
                if question:
                    quiz.add_question(question)
            
            # Generate quiz nodes
            quiz.generate_quiz_nodes()
            
            logger.info(f"Generated quiz: {len(quiz.questions)} questions, {len(quiz.outcomes)} outcomes")
            return quiz
            
        except Exception as e:
            logger.error(f"Error generating quiz: {e}")
            return None
    
    def _generate_quiz_structure(self, prompt: str, quiz_type: QuizType) -> Optional[Dict[str, Any]]:
        """Generate quiz structure using AI"""
        try:
            # Create AI prompt for quiz generation
            ai_prompt = self._create_quiz_generation_prompt(prompt, quiz_type)
            
            # Get AI response
            response = self.ollama_client.generate_text(ai_prompt)
            if not response.success:
                logger.error(f"AI generation failed: {response.error}")
                return None
            
            # Parse AI response
            quiz_structure = self._parse_quiz_response(response.text)
            return quiz_structure
            
        except Exception as e:
            logger.error(f"Error generating quiz structure: {e}")
            return None
    
    def _create_quiz_generation_prompt(self, user_prompt: str, quiz_type: QuizType) -> str:
        """Create AI prompt for quiz generation"""
        
        if quiz_type == QuizType.PERSONALITY:
            prompt_template = f"""
Create a personality quiz based on this prompt: "{user_prompt}"

Generate a JSON structure with:
1. Quiz metadata (title, description, hashtags)
2. 4-6 possible outcomes/results
3. 5-8 questions with 4 choices each
4. Each choice should have weights for different outcomes

Format as valid JSON:
{{
    "metadata": {{
        "title": "Quiz Title",
        "description": "Quiz description",
        "hashtags": ["#tag1", "#tag2"],
        "viral_potential": "high"
    }},
    "outcomes": [
        {{
            "id": "outcome1",
            "name": "Outcome Name",
            "title": "🎯 You are...",
            "description": "Detailed description",
            "emoji": "🎯",
            "fun_fact": "Interesting fact",
            "rarity": "common"
        }}
    ],
    "questions": [
        {{
            "text": "Question text?",
            "choices": [
                {{
                    "text": "Choice text",
                    "outcome_weights": {{"outcome1": 3, "outcome2": 1}}
                }}
            ]
        }}
    ]
}}

Make it engaging and shareable!
"""
        
        elif quiz_type == QuizType.COMPATIBILITY:
            prompt_template = f"""
Create a "Which character/type are you?" quiz based on: "{user_prompt}"

Generate a JSON structure with:
1. Quiz metadata
2. 4-8 character/type outcomes
3. 5-10 questions that determine compatibility
4. Weighted choices that map to different characters

Make it fun and viral-worthy!
"""
        
        elif quiz_type == QuizType.KNOWLEDGE:
            prompt_template = f"""
Create a knowledge/trivia quiz based on: "{user_prompt}"

Generate a JSON structure with:
1. Quiz metadata
2. Score-based outcomes (expert, intermediate, beginner)
3. 8-15 questions with correct/incorrect answers
4. Educational and entertaining content

Focus on testing knowledge while being engaging!
"""
        
        else:
            prompt_template = f"""
Create an engaging quiz based on: "{user_prompt}"

Generate a JSON structure with appropriate outcomes and questions.
Make it shareable and fun!
"""
        
        return prompt_template
    
    def _parse_quiz_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """Parse AI response into quiz structure"""
        try:
            # Try to extract JSON from response
            response_text = response_text.strip()
            
            # Find JSON block
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                logger.error("No JSON found in AI response")
                return None
            
            json_text = response_text[start_idx:end_idx]
            quiz_data = json.loads(json_text)
            
            # Validate structure
            if not self._validate_quiz_structure(quiz_data):
                logger.error("Invalid quiz structure from AI")
                return None
            
            return quiz_data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error parsing quiz response: {e}")
            return None
    
    def _validate_quiz_structure(self, quiz_data: Dict[str, Any]) -> bool:
        """Validate quiz structure"""
        try:
            # Check required fields
            if 'metadata' not in quiz_data:
                return False
            
            if 'outcomes' not in quiz_data or not quiz_data['outcomes']:
                return False
            
            if 'questions' not in quiz_data or not quiz_data['questions']:
                return False
            
            # Validate outcomes
            for outcome in quiz_data['outcomes']:
                if not all(key in outcome for key in ['id', 'name', 'title', 'description']):
                    return False
            
            # Validate questions
            for question in quiz_data['questions']:
                if 'text' not in question or 'choices' not in question:
                    return False
                
                if len(question['choices']) < 2:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating quiz structure: {e}")
            return False
    
    def _create_outcome_from_data(self, outcome_data: Dict[str, Any]) -> Optional[QuizOutcome]:
        """Create QuizOutcome from data"""
        try:
            outcome = QuizOutcome(
                id=outcome_data['id'],
                name=outcome_data['name'],
                title=outcome_data['title'],
                description=outcome_data['description'],
                result_type=QuizResultType.CATEGORY,  # Default
                emoji=outcome_data.get('emoji', '🎯'),
                color_scheme=outcome_data.get('color_scheme', '#3498db'),
                share_text=outcome_data.get('share_text', f"I got {outcome_data['name']}!"),
                hashtags=outcome_data.get('hashtags', []),
                fun_fact=outcome_data.get('fun_fact', ''),
                rarity=outcome_data.get('rarity', 'common')
            )
            return outcome
            
        except Exception as e:
            logger.error(f"Error creating outcome: {e}")
            return None
    
    def _create_question_from_data(self, question_data: Dict[str, Any]) -> Optional[QuizQuestion]:
        """Create QuizQuestion from data"""
        try:
            # Process choices
            choices = []
            for choice_data in question_data['choices']:
                choice = {
                    'text': choice_data['text'],
                    'outcome_weights': choice_data.get('outcome_weights', {})
                }
                choices.append(choice)
            
            question = QuizQuestion(
                id=str(uuid.uuid4()),
                text=question_data['text'],
                question_number=len(choices),  # Will be set properly later
                choices=choices,
                category=question_data.get('category', ''),
                difficulty=question_data.get('difficulty', 'medium'),
                explanation=question_data.get('explanation', ''),
                image_prompt=question_data.get('image_prompt', ''),
                video_prompt=question_data.get('video_prompt', '')
            )
            return question
            
        except Exception as e:
            logger.error(f"Error creating question: {e}")
            return None
    
    def generate_quiz_from_topic(self, topic: str, num_questions: int = 6, 
                                num_outcomes: int = 4) -> Optional[QuizWeb]:
        """Generate quiz from a simple topic"""
        prompt = f"Create a personality quiz about '{topic}' with {num_outcomes} different results and {num_questions} questions."
        return self.generate_quiz_from_prompt(prompt, "personality")
    
    def enhance_existing_quiz(self, quiz: QuizWeb, enhancement_prompt: str) -> bool:
        """Enhance an existing quiz with AI"""
        try:
            # Generate enhancement suggestions
            ai_prompt = f"""
Enhance this quiz based on the request: "{enhancement_prompt}"

Current quiz:
- Title: {quiz.metadata.get('title', 'Unknown')}
- Questions: {len(quiz.questions)}
- Outcomes: {len(quiz.outcomes)}

Suggest improvements in JSON format:
{{
    "new_questions": [...],
    "improved_outcomes": [...],
    "metadata_updates": {{...}}
}}
"""
            
            response = self.ollama_client.generate_text(ai_prompt)
            if not response.success:
                return False
            
            # Parse and apply enhancements
            enhancements = self._parse_quiz_response(response.text)
            if enhancements:
                self._apply_quiz_enhancements(quiz, enhancements)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error enhancing quiz: {e}")
            return False
    
    def _apply_quiz_enhancements(self, quiz: QuizWeb, enhancements: Dict[str, Any]):
        """Apply enhancements to existing quiz"""
        try:
            # Add new questions
            for question_data in enhancements.get('new_questions', []):
                question = self._create_question_from_data(question_data)
                if question:
                    question.question_number = len(quiz.questions) + 1
                    quiz.add_question(question)
            
            # Update metadata
            metadata_updates = enhancements.get('metadata_updates', {})
            quiz.metadata.update(metadata_updates)
            
            # Regenerate nodes with new content
            quiz.generate_quiz_nodes()
            
            logger.info("Applied quiz enhancements successfully")
            
        except Exception as e:
            logger.error(f"Error applying enhancements: {e}")
    
    def generate_viral_quiz_ideas(self, theme: str) -> List[str]:
        """Generate viral quiz ideas for a theme"""
        try:
            ai_prompt = f"""
Generate 10 viral quiz ideas related to "{theme}".

Each idea should be:
- Highly shareable
- Engaging and fun
- Appeal to a broad audience
- Have clear, distinct outcomes

Format as a simple list:
1. Quiz idea 1
2. Quiz idea 2
...
"""
            
            response = self.ollama_client.generate_text(ai_prompt)
            if response.success:
                # Parse ideas from response
                ideas = []
                lines = response.text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and (line[0].isdigit() or line.startswith('-')):
                        # Remove numbering
                        idea = line.split('.', 1)[-1].strip()
                        if idea:
                            ideas.append(idea)
                
                return ideas[:10]  # Return up to 10 ideas
            
            return []
            
        except Exception as e:
            logger.error(f"Error generating quiz ideas: {e}")
            return []
