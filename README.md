# CYOA Automation System

A comprehensive Python application that automates the creation, management, and posting of Choose Your Own Adventure (CYOA) stories on X (formerly Twitter) using exclusively local AI models and resources.

## Features

### Core Functionality
- **Local AI Story Generation**: Uses Ollama (LLaMA 3.1) for story creation
- **Interactive Story Web**: DAG-based story structure with multiple entry/exit points
- **Inventory System**: Track items that influence story choices and paths
- **Class System**: Character classes (<PERSON><PERSON>, <PERSON>, Charmer) that modify available choices
- **Content Rating**: "Spicy" rating system for adult content with inheritance
- **Scoring System**: Calculate scores for death endings based on path efficiency
- **Path Funneling**: Avoid forced endings with side missions and intermediary nodes

### Media Generation
- **AI Video Generation**: ComfyUI integration with ComfyScript for video creation
- **Lip-Sync Audio**: Synchronized audio using Wav2Lip or TTS models
- **Automated Prompts**: LLM-generated visual and audio prompts

### Social Media Integration
- **X (Twitter) Posting**: Automated posting with media, links, and paywalls
- **Premium Content**: Paywall restrictions for X subscribers
- **Content Warnings**: Automatic spicy content tagging

### User Interface
- **PyQt5 GUI**: Comprehensive interface for story management
- **Graph Visualization**: Interactive node graph with drag-and-drop editing
- **Story Editor**: Tree view and node editing capabilities
- **InkPython Integration**: Local story playback and testing

## Installation

### Prerequisites

- Python 3.10+
- NVIDIA GPU with ≥12GB VRAM (recommended, CPU fallback supported)
- ≥32GB RAM
- ≥100GB storage

## 🚀 Quick Start

### One-Command Setup
```bash
# Install Pixi (if not already installed)
curl -fsSL https://pixi.sh/install.sh | bash

# Clone and setup everything automatically
git clone <repository-url>
cd cyoax
pixi run first-run
```

This single command will:
- ✅ Install all Python dependencies (including PyQt5)
- ✅ Check system requirements
- ✅ Download AI models
- ✅ Create necessary directories
- ✅ Set up configuration files
- ✅ Validate the installation

### Daily Usage
```bash
# Create stories with the beautiful CLI wizard
pixi run wizard

# Launch the full GUI application
pixi run run

# Check system health anytime
pixi run health

# View all available commands
pixi task list
```

### Your First Story in 2 Minutes
1. **Run**: `pixi run wizard`
2. **Choose**: "Import from existing text"
3. **Paste**: Any story text (fairy tale, book chapter, etc.)
4. **Watch**: AI creates interactive story with characters
5. **Explore**: `pixi run run` to see your story in the GUI

### Setup with Pixi Manager (Manual)

If you prefer manual setup:

1. **Install Pixi Manager**:
   ```bash
   curl -fsSL https://pixi.sh/install.sh | bash
   ```

2. **Clone and Initialize Project**:
   ```bash
   git clone <repository-url>
   cd cyoax
   pixi install
   ```

3. **Install Ollama**:
   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ollama pull llama3.1
   ```

4. **Setup Environment**:
   ```bash
   # .env file is created automatically by first-run
   # Edit it to add your X API credentials
   ```

### Manual Installation

If you prefer not to use Pixi:

```bash
pip install -r requirements.txt
# Follow Ollama and ComfyUI setup steps above
```

## Configuration

### Environment Variables (.env)

```bash
# X (Twitter) API Credentials
X_API_KEY=your_api_key_here
X_API_SECRET=your_api_secret_here
X_ACCESS_TOKEN=your_access_token_here
X_ACCESS_TOKEN_SECRET=your_access_token_secret_here
X_BEARER_TOKEN=your_bearer_token_here

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1

# ComfyUI Configuration
COMFYUI_BASE_URL=http://127.0.0.1:8188
```

### Story Configuration (config.json)

Key settings include:
- `num_entry_points`: Number of story starting points (default: 3)
- `max_nodes`: Maximum nodes in story web (default: 100)
- `initial_inventory`: Starting player items (default: {"gold": 100})
- `classes`: Available character classes
- `spicy_keywords`: Keywords for content rating

## Usage

### Available Commands

Pixi provides many convenient commands:

```bash
# Story Creation
pixi run wizard          # Beautiful CLI story creation wizard
pixi run run            # Launch full GUI application

# System Management
pixi run health         # Check system health
pixi run first-run      # Complete first-time setup
pixi run backup         # Create manual backup

# Development
pixi run test           # Run all tests
pixi run test-story     # Test story generation
pixi run lint           # Code quality checks
pixi run format         # Auto-format code

# External Services
pixi run start-ollama   # Start Ollama server
pixi run start-comfyui  # Start ComfyUI server
pixi run check-ollama   # Check if Ollama is running
pixi run check-comfyui  # Check if ComfyUI is running

# Media and Social
pixi run generate       # Generate story from CLI
pixi run media          # Generate media for stories
pixi run post           # Post to X (Twitter)
```

### Starting the Application

**Option 1: CLI Wizard (Recommended for beginners)**
```bash
pixi run wizard
```

**Option 2: Full GUI**
```bash
# Start services (if not already running)
pixi run start-ollama

# Launch application
pixi run run
```

**Option 3: Development Mode**
```bash
# Start development environment with file watching
pixi run dev
```

### Using the GUI

- **Story Editor**: Import storylines, edit nodes, manage structure
- **Characters Tab**: Manage character profiles and consistency
- **Graph Viewer**: Visualize story connections and flow
- **X Manager**: Generate media and post to social media

### Creating Stories

#### Import from Text
1. Go to File → Import Storyline
2. Enter title and select character class
3. Paste source text (e.g., fairy tale)
4. Click Import to generate CYOA structure

#### Manual Creation
1. File → New Story
2. Add nodes using Story Editor
3. Define choices and connections
4. Set ratings and premium flags

### Story Elements

#### Inventory System
- Items influence available choices
- Gold for purchases, tools for actions
- Automatic inventory state tracking

#### Class System
- **Mage**: Magic-based solutions, spell casting
- **Ranger**: Survival skills, tracking, hunting
- **Charmer**: Social interaction, seduction, persuasion

#### Content Rating
- **Safe**: General audience content
- **Spicy**: Romantic/adult themes with warnings
- Automatic inheritance through story paths

### Media Generation

1. **Generate Videos**: Creates visual content for each node
2. **Generate Audio**: TTS narration with lip-sync
3. **Preview Posts**: Review content before posting
4. **Post to X**: Automated social media publishing

## Project Structure

```
cyoax/
├── src/
│   ├── story/          # Story generation and management
│   ├── media/          # Video and audio generation
│   ├── social/         # X (Twitter) integration
│   ├── gui/            # PyQt5 user interface
│   └── utils/          # Utility functions
├── data/
│   ├── storylines/     # Saved story files
│   └── templates/      # Sample story texts
├── workflows/          # ComfyUI workflow definitions
├── videos/             # Generated video output
├── logs/               # Application logs
├── config.json         # Configuration settings
├── pixi.toml          # Pixi project definition
└── main.py            # Application entry point
```

## API Integration

### Ollama (Local LLM)
- Story generation and text processing
- Prompt crafting for media generation
- Content analysis and rating

### ComfyUI (Media Generation)
- Video creation with Stable Diffusion
- Audio generation and processing
- Lip-sync integration

### X API (Social Media)
- Automated posting with media
- Paywall and premium content
- Rate limiting and error handling

## Development

### Running Tests
```bash
pixi run test
```

### Code Formatting
```bash
pixi run format
pixi run lint
```

### Adding New Features
1. Create feature branch
2. Implement in appropriate module
3. Add tests and documentation
4. Submit pull request

## Troubleshooting

### Common Issues

1. **Ollama Connection Failed**:
   - Ensure Ollama is running: `ollama serve`
   - Check model is installed: `ollama list`

2. **ComfyUI Not Available**:
   - Start ComfyUI server
   - Check GPU memory availability
   - Use CPU fallback if needed

3. **X API Errors**:
   - Verify API credentials in .env
   - Check rate limits and quotas
   - Ensure proper permissions

### Logs
Check logs in the `logs/` directory:
- `cyoa_automation.log`: Main application log
- `story.log`: Story generation logs
- `media.log`: Media generation logs
- `social.log`: Social media logs

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## License

[License information to be added]

## Support

For issues and questions:
- Check the logs for error details
- Review configuration settings
- Ensure all dependencies are installed
- Verify API credentials and services

## Roadmap

### Planned Features
- Advanced graph visualization
- Multi-language support
- Extended rating system
- Story collaboration tools
- Analytics and metrics
- Mobile companion app

### Performance Optimizations
- Batch video generation
- Caching improvements
- Memory optimization
- Parallel processing
