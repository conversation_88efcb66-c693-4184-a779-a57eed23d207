# CYOA Automation System - Deployment Guide

## Overview

This guide covers the complete deployment of the CYOA Automation System, including all components for story generation, media creation, and X (Twitter) posting with clickable video choices.

## System Requirements

### Hardware Requirements
- **CPU**: 8+ cores recommended for parallel processing
- **RAM**: 32GB+ recommended (16GB minimum)
- **GPU**: NVIDIA GPU with 12GB+ VRAM for ComfyUI (optional, CPU fallback available)
- **Storage**: 100GB+ free space for videos and models
- **Network**: Stable internet connection for X API and model downloads

### Software Requirements
- **OS**: Linux, macOS, or Windows 10/11
- **Python**: 3.10 or higher
- **Git**: For cloning repositories
- **FFmpeg**: For video processing

## Installation Steps

### 1. System Preparation

#### Install Python 3.10+
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.10 python3.10-pip python3.10-venv

# macOS (with Homebrew)
brew install python@3.10

# Windows
# Download from python.org
```

#### Install FFmpeg
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# Download from ffmpeg.org or use chocolatey:
# choco install ffmpeg
```

### 2. Project Setup

#### Clone Repository
```bash
git clone <repository-url>
cd cyoax
```

#### Option A: Using Pixi Manager (Recommended)
```bash
# Install Pixi
curl -fsSL https://pixi.sh/install.sh | bash

# Initialize project
pixi install

# Activate environment
pixi shell
```

#### Option B: Manual Setup
```bash
# Create virtual environment
python3.10 -m venv venv
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt
```

### 3. AI Model Setup

#### Install Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.com/install.sh | sh

# Windows
# Download from ollama.com
```

#### Download LLM Model
```bash
ollama pull llama3.1
# or for smaller systems:
ollama pull llama3.1:8b
```

#### Start Ollama Server
```bash
ollama serve
```

### 4. ComfyUI Setup (Optional but Recommended)

#### Install ComfyUI
```bash
git clone https://github.com/comfyanonymous/ComfyUI.git
cd ComfyUI
pip install -r requirements.txt
pip install comfy-script
```

#### Download Models
```bash
# Download Stable Diffusion model
cd models/checkpoints
wget https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.safetensors

# Return to project root
cd ../../..
```

#### Start ComfyUI
```bash
cd ComfyUI
python main.py
# ComfyUI will be available at http://127.0.0.1:8188
```

### 5. X (Twitter) API Setup

#### Get X API Credentials
1. Go to [developer.twitter.com](https://developer.twitter.com)
2. Create a new app
3. Generate API keys and tokens
4. Apply for Elevated access if needed

#### Configure Environment Variables
```bash
cp .env.example .env
# Edit .env with your credentials:
```

```bash
# X (Twitter) API Credentials
X_API_KEY=your_api_key_here
X_API_SECRET=your_api_secret_here
X_ACCESS_TOKEN=your_access_token_here
X_ACCESS_TOKEN_SECRET=your_access_token_secret_here
X_BEARER_TOKEN=your_bearer_token_here

# Optional: Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1

# Optional: ComfyUI Configuration
COMFYUI_BASE_URL=http://127.0.0.1:8188
```

### 6. System Validation

#### Run Setup Script
```bash
python scripts/setup.py
```

#### Test Core Functionality
```bash
# Test basic story creation
python scripts/demo.py

# Test AI generation (requires Ollama)
python scripts/test_generation.py

# Test full pipeline
python scripts/test_full_pipeline.py
```

## Configuration

### Story Generation Settings
Edit `config.json` to customize:

```json
{
  "story_generation": {
    "num_entry_points": 3,
    "max_nodes": 100,
    "min_deaths": 3,
    "min_successes": 2
  }
}
```

### Media Generation Settings
```json
{
  "media_generation": {
    "video_quality": "720p",
    "video_duration_seconds": 10,
    "max_file_size_mb": 15
  }
}
```

### Social Media Settings
```json
{
  "social_media": {
    "post_delay_seconds": 10,
    "character_limit": 280
  }
}
```

## Running the Application

### Start All Services
```bash
# Terminal 1: Start Ollama
ollama serve

# Terminal 2: Start ComfyUI (optional)
cd ComfyUI && python main.py

# Terminal 3: Start CYOA Application
python main.py
```

### Using the GUI
1. **Story Editor Tab**: Import storylines, edit nodes
2. **Graph Viewer Tab**: Visualize story structure
3. **X Manager Tab**: Generate media and post to X

### Command Line Usage
```bash
# Generate story from text file
python scripts/generate_story.py --input data/templates/hansel_gretel.txt --class Mage

# Test specific components
python scripts/test_generation.py
python scripts/test_full_pipeline.py
```

## X Posting Strategies

### Strategy 1: Video Ads with Clickable Choices (Future)
- Requires X Ads API access
- Videos have clickable overlays
- Direct navigation between story nodes
- Premium monetization through ads

### Strategy 2: Link-Based Navigation (Current)
- Post videos with choice links above
- Links point to subsequent posts
- Fallback for standard X API
- Works with current implementation

### Implementation Notes
The system is designed to support both strategies:

1. **Video Ads**: Set `enable_video_ads: true` in config when X Ads API is available
2. **Link Navigation**: Default mode, works immediately with standard X API

## Monetization Setup

### X Super Follows Integration
1. Enable Super Follows in X Creator Studio
2. Set pricing tiers:
   - Premium: $4.99/month
   - Spicy: $9.99/month
3. Configure webhook for subscription events
4. Link to paywall system

### Content Gating
- Premium content requires subscription
- Spicy content has additional warnings
- Free users see teasers with subscription prompts

## Troubleshooting

### Common Issues

#### Ollama Connection Failed
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Restart Ollama
ollama serve
```

#### ComfyUI Not Available
```bash
# Check ComfyUI status
curl http://127.0.0.1:8188/system_stats

# Restart ComfyUI
cd ComfyUI && python main.py
```

#### X API Errors
- Verify credentials in `.env`
- Check API rate limits
- Ensure proper permissions

#### Video Generation Issues
- Check GPU memory availability
- Verify ComfyUI models are downloaded
- Use CPU fallback if needed

### Log Files
Check logs in the `logs/` directory:
- `cyoa_automation.log`: Main application
- `story.log`: Story generation
- `media.log`: Media generation
- `social.log`: X posting

## Performance Optimization

### For Large Stories
- Increase `max_nodes` gradually
- Use batch processing for media generation
- Implement caching for repeated content

### For Limited Resources
- Use smaller LLM models (llama3.1:8b)
- Reduce video quality to 480p
- Enable CPU fallback for ComfyUI

### For High Volume
- Implement queue management
- Use multiple worker threads
- Set up load balancing

## Security Considerations

### API Keys
- Never commit API keys to version control
- Use environment variables
- Rotate keys regularly

### Content Moderation
- Review generated content before posting
- Implement content filters
- Monitor for policy violations

### User Data
- Encrypt subscriber information
- Implement GDPR compliance
- Regular security audits

## Scaling and Production

### Production Deployment
1. Use production-grade database (PostgreSQL)
2. Implement proper logging and monitoring
3. Set up automated backups
4. Use container orchestration (Docker/Kubernetes)

### Monitoring
- Set up health checks for all services
- Monitor API rate limits
- Track generation success rates
- Monitor subscriber metrics

### Backup Strategy
- Regular database backups
- Story file versioning
- Media file archival
- Configuration backups

## Support and Maintenance

### Regular Tasks
- Update AI models monthly
- Review and update content policies
- Monitor system performance
- Update dependencies

### Troubleshooting Resources
- Check system logs first
- Verify all services are running
- Test individual components
- Review configuration settings

### Getting Help
- Check documentation and logs
- Test with minimal examples
- Verify system requirements
- Review recent changes
