#!/usr/bin/env python3
"""
Health Check CLI - Beautiful system health monitoring
"""

import sys
import json
from pathlib import Path

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.text import Text
    from rich.layout import Layout
    from rich.live import Live
    import typer
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from utils.system_health import SystemHealthChecker
    from utils.backup_manager import BackupManager
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run health")
    sys.exit(1)

if RICH_AVAILABLE:
    app = typer.Typer(help="🏥 System Health Monitor")
    console = Console()
else:
    class SimpleApp:
        def command(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
    app = SimpleApp()
    
    class SimpleConsole:
        def print(self, *args, **kwargs):
            print(*args)
    console = SimpleConsole()


@app.command()
def check(
    detailed: bool = typer.Option(False, "--detailed", "-d", help="Show detailed information"),
    json_output: bool = typer.Option(False, "--json", "-j", help="Output as JSON"),
    fix: bool = typer.Option(False, "--fix", help="Attempt to fix issues automatically")
):
    """Run comprehensive system health check"""
    
    if json_output:
        run_json_check()
        return
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold blue]🏥 System Health Check[/bold blue]\n"
            "[dim]Checking all system components and dependencies[/dim]",
            border_style="blue"
        ))
    else:
        print("🏥 System Health Check")
        print("Checking all system components and dependencies")
    
    # Load configuration
    config = load_config()
    health_checker = SystemHealthChecker(config)
    
    # Run health checks with progress
    if RICH_AVAILABLE:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running health checks...", total=None)
            checks = health_checker.run_all_checks()
            progress.update(task, completed=True)
    else:
        print("Running health checks...")
        checks = health_checker.run_all_checks()
    
    # Display results
    display_health_results(checks, health_checker, detailed)
    
    # Attempt fixes if requested
    if fix:
        attempt_fixes(checks, config)


@app.command()
def monitor(
    interval: int = typer.Option(30, "--interval", "-i", help="Check interval in seconds"),
    continuous: bool = typer.Option(False, "--continuous", "-c", help="Run continuously")
):
    """Monitor system health continuously"""
    
    if not RICH_AVAILABLE:
        print("Continuous monitoring requires Rich. Install with: pip install rich")
        return
    
    import time
    
    console.print(Panel.fit(
        "[bold green]📊 Health Monitor[/bold green]\n"
        f"[dim]Monitoring every {interval} seconds[/dim]",
        border_style="green"
    ))
    
    config = load_config()
    health_checker = SystemHealthChecker(config)
    
    try:
        while True:
            # Create layout
            layout = Layout()
            layout.split_column(
                Layout(name="header", size=3),
                Layout(name="body"),
                Layout(name="footer", size=3)
            )
            
            # Header
            layout["header"].update(Panel(
                f"[bold]System Health Monitor[/bold] - {time.strftime('%Y-%m-%d %H:%M:%S')}",
                style="blue"
            ))
            
            # Run checks
            checks = health_checker.run_all_checks()
            overall_status = health_checker.get_overall_status()
            
            # Create health table
            table = create_health_table(checks)
            layout["body"].update(Panel(table, title="Component Status"))
            
            # Footer
            status_color = "green" if overall_status.value == "healthy" else "yellow" if overall_status.value == "warning" else "red"
            layout["footer"].update(Panel(
                f"[{status_color}]Overall Status: {overall_status.value.upper()}[/{status_color}] | "
                f"Next check in {interval}s | Press Ctrl+C to exit",
                style="dim"
            ))
            
            # Display
            with Live(layout, refresh_per_second=1, console=console):
                time.sleep(interval)
            
            if not continuous:
                break
                
    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped[/yellow]")


@app.command()
def services():
    """Check external service status"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit("[bold]🔧 External Services Status[/bold]", border_style="cyan"))
    else:
        print("🔧 External Services Status")
    
    services = [
        ("Ollama", "http://localhost:11434/api/tags"),
        ("ComfyUI", "http://127.0.0.1:8188/system_stats"),
    ]
    
    if RICH_AVAILABLE:
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Service", style="cyan")
        table.add_column("URL", style="blue")
        table.add_column("Status", style="white")
        table.add_column("Response", style="dim")
    
    import requests
    
    for service_name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                status = "[green]✅ Online[/green]" if RICH_AVAILABLE else "✅ Online"
                response_text = f"{response.status_code} OK"
            else:
                status = "[yellow]⚠️ Issues[/yellow]" if RICH_AVAILABLE else "⚠️ Issues"
                response_text = f"{response.status_code}"
        except requests.RequestException as e:
            status = "[red]❌ Offline[/red]" if RICH_AVAILABLE else "❌ Offline"
            response_text = str(e)[:50]
        
        if RICH_AVAILABLE:
            table.add_row(service_name, url, status, response_text)
        else:
            print(f"{service_name}: {status} - {response_text}")
    
    if RICH_AVAILABLE:
        console.print(table)


def display_health_results(checks, health_checker, detailed: bool):
    """Display health check results"""
    if RICH_AVAILABLE:
        table = create_health_table(checks, detailed)
        console.print(table)
        
        # Overall status
        overall_status = health_checker.get_overall_status()
        status_color = "green" if overall_status.value == "healthy" else "yellow" if overall_status.value == "warning" else "red"
        console.print(f"\n[{status_color}]Overall Status: {overall_status.value.upper()}[/{status_color}]")
        
        # Recommendations
        summary = health_checker.get_health_summary()
        if summary['recommendations']:
            console.print("\n[bold]📋 Recommendations:[/bold]")
            for rec in summary['recommendations'][:5]:
                console.print(f"  • {rec}")
    else:
        print("\nHealth Check Results:")
        for name, check in checks.items():
            status = "✅" if check.status.value == "healthy" else "⚠️" if check.status.value == "warning" else "❌"
            print(f"  {status} {name}: {check.message}")
        
        overall_status = health_checker.get_overall_status()
        print(f"\nOverall Status: {overall_status.value.upper()}")


def create_health_table(checks, detailed: bool = False):
    """Create health status table"""
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="white")
    table.add_column("Message", style="white")
    
    if detailed:
        table.add_column("Details", style="dim")
    
    for name, check in checks.items():
        status_style = "green" if check.status.value == "healthy" else "yellow" if check.status.value == "warning" else "red"
        status_icon = "✅" if check.status.value == "healthy" else "⚠️" if check.status.value == "warning" else "❌"
        
        row = [
            name,
            f"[{status_style}]{status_icon} {check.status.value.upper()}[/{status_style}]",
            check.message
        ]
        
        if detailed and check.details:
            details_text = ", ".join([f"{k}: {v}" for k, v in list(check.details.items())[:3]])
            row.append(details_text[:50] + "..." if len(details_text) > 50 else details_text)
        
        table.add_row(*row)
    
    return table


def attempt_fixes(checks, config):
    """Attempt to fix common issues"""
    if RICH_AVAILABLE:
        console.print("\n[yellow]🔧 Attempting to fix issues...[/yellow]")
    else:
        print("\n🔧 Attempting to fix issues...")
    
    fixes_applied = 0
    
    for name, check in checks.items():
        if check.status.value == "error":
            if name == "Storage":
                # Try to create missing directories
                try:
                    directories = ['data', 'logs', 'videos', 'backups']
                    for directory in directories:
                        Path(directory).mkdir(parents=True, exist_ok=True)
                    console.print(f"[green]✅ Fixed: Created missing directories[/green]")
                    fixes_applied += 1
                except Exception as e:
                    console.print(f"[red]❌ Failed to fix {name}: {e}[/red]")
            
            elif name == "Configuration":
                # Try to create basic config
                try:
                    if not Path("config.json").exists():
                        basic_config = {
                            "story_generation": {"max_nodes": 25},
                            "media_generation": {"video_quality": "720p"},
                            "character_system": {"auto_extract_characters": True}
                        }
                        with open("config.json", "w") as f:
                            json.dump(basic_config, f, indent=2)
                        console.print(f"[green]✅ Fixed: Created basic configuration[/green]")
                        fixes_applied += 1
                except Exception as e:
                    console.print(f"[red]❌ Failed to fix {name}: {e}[/red]")
    
    if fixes_applied > 0:
        console.print(f"\n[green]🎉 Applied {fixes_applied} fixes. Re-run health check to verify.[/green]")
    else:
        console.print("\n[yellow]No automatic fixes available. Check recommendations above.[/yellow]")


def run_json_check():
    """Run health check and output JSON"""
    config = load_config()
    health_checker = SystemHealthChecker(config)
    checks = health_checker.run_all_checks()
    summary = health_checker.get_health_summary()
    
    print(json.dumps(summary, indent=2))


def load_config() -> dict:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def main():
    """Main function for non-typer usage"""
    if len(sys.argv) == 1:
        # Default behavior
        if RICH_AVAILABLE:
            check(detailed=False, json_output=False, fix=False)
        else:
            print("Health check requires Rich. Install with: pip install rich")
    else:
        if RICH_AVAILABLE:
            app()
        else:
            print("Advanced features require Rich. Install with: pip install rich")


if __name__ == "__main__":
    if RICH_AVAILABLE:
        app()
    else:
        main()
