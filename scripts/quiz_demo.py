#!/usr/bin/env python3
"""
Quiz Demo - Showcase the quiz system capabilities
Creates sample quizzes and demonstrates viral potential
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn
    import typer
    
    from quiz.quiz_templates import QuizTemplateManager
    from quiz.quiz_generator import QuizGenerator
    from quiz.quiz_system import QuizWeb, QuizType
    from utils.ollama_client import OllamaClient
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Quiz demo requires Rich and quiz system components")
    sys.exit(1)

app = typer.Typer(help="🧠 Quiz System Demo")
console = Console()


@app.command()
def full():
    """Run complete quiz system demo"""
    
    console.print(Panel.fit(
        "[bold magenta]🧠 Quiz System Demo[/bold magenta]\n"
        "[dim]Showcasing viral quiz creation capabilities[/dim]",
        border_style="magenta"
    ))
    
    # Demo steps
    steps = [
        ("Quiz Templates", demo_templates),
        ("<PERSON> House Quiz", demo_harry_potter),
        ("Marvel Character Quiz", demo_marvel),
        ("AI Quiz Generation", demo_ai_generation),
        ("Quiz Statistics", demo_statistics),
        ("Viral Potential", demo_viral_potential),
    ]
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        main_task = progress.add_task("Running quiz demo...", total=len(steps))
        
        for step_name, step_func in steps:
            progress.update(main_task, description=f"Demo: {step_name}")
            step_func()
            progress.advance(main_task)
    
    show_demo_summary()


def demo_templates():
    """Demo quiz templates"""
    console.print("\n[bold blue]📚 Quiz Templates Demo[/bold blue]")
    
    try:
        config = load_config()
        template_manager = QuizTemplateManager(config)
        templates = template_manager.list_templates()
        
        table = Table(title="Available Quiz Templates")
        table.add_column("Template", style="cyan")
        table.add_column("Type", style="blue")
        table.add_column("Questions", style="yellow")
        table.add_column("Viral Potential", style="red")
        
        for template in templates:
            viral_indicator = "🔥🔥🔥" if template['viral_potential'] == 'very_high' else "🔥🔥" if template['viral_potential'] == 'high' else "🔥"
            table.add_row(
                template['title'],
                template['quiz_type'],
                str(template['questions']),
                viral_indicator
            )
        
        console.print(table)
        console.print(f"[green]✅ {len(templates)} quiz templates available[/green]")
        
    except Exception as e:
        console.print(f"[red]❌ Template demo failed: {e}[/red]")


def demo_harry_potter():
    """Demo Harry Potter house quiz"""
    console.print("\n[bold green]🏰 Harry Potter House Quiz Demo[/bold green]")
    
    try:
        config = load_config()
        template_manager = QuizTemplateManager(config)
        
        # Create Harry Potter quiz
        quiz = template_manager.create_quiz_from_template('harry_potter_house', 'Demo HP Quiz')
        
        if quiz:
            console.print(f"[green]✅ Created Harry Potter quiz[/green]")
            
            # Show quiz details
            table = Table(show_header=False, box=None)
            table.add_column("Detail", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("🏠 Houses", str(len(quiz.outcomes)))
            table.add_row("❓ Questions", str(len(quiz.questions)))
            table.add_row("📄 Total Nodes", str(len(quiz.nodes)))
            table.add_row("📱 Estimated Posts", str(quiz.get_quiz_stats()['estimated_posts']))
            
            console.print(table)
            
            # Show sample outcomes
            console.print("\n[dim]Sample Houses:[/dim]")
            for outcome in list(quiz.outcomes.values())[:2]:
                console.print(f"  {outcome.emoji} {outcome.name}: {outcome.description[:60]}...")
        else:
            console.print("[yellow]⚠️  Failed to create Harry Potter quiz[/yellow]")
        
    except Exception as e:
        console.print(f"[red]❌ Harry Potter demo failed: {e}[/red]")


def demo_marvel():
    """Demo Marvel character quiz"""
    console.print("\n[bold red]🦸 Marvel Character Quiz Demo[/bold red]")
    
    try:
        config = load_config()
        template_manager = QuizTemplateManager(config)
        
        # Create Marvel quiz
        quiz = template_manager.create_quiz_from_template('marvel_character', 'Demo Marvel Quiz')
        
        if quiz:
            console.print(f"[green]✅ Created Marvel character quiz[/green]")
            
            # Show characters
            console.print("\n[dim]Marvel Heroes:[/dim]")
            for outcome in quiz.outcomes.values():
                console.print(f"  {outcome.emoji} {outcome.name}")
            
            # Show sample question
            if quiz.questions:
                sample_q = list(quiz.questions.values())[0]
                console.print(f"\n[dim]Sample Question:[/dim] {sample_q.text}")
                for choice in sample_q.choices[:2]:
                    console.print(f"  • {choice['text']}")
        else:
            console.print("[yellow]⚠️  Failed to create Marvel quiz[/yellow]")
        
    except Exception as e:
        console.print(f"[red]❌ Marvel demo failed: {e}[/red]")


def demo_ai_generation():
    """Demo AI-powered quiz generation"""
    console.print("\n[bold cyan]🤖 AI Quiz Generation Demo[/bold cyan]")
    
    try:
        config = load_config()
        ollama_client = OllamaClient()
        
        if not ollama_client.is_available():
            console.print("[yellow]⚠️  Ollama not available - skipping AI generation demo[/yellow]")
            return
        
        quiz_generator = QuizGenerator(config, ollama_client)
        
        # Generate a simple quiz
        console.print("[blue]Generating 'What type of programmer are you?' quiz...[/blue]")
        
        quiz = quiz_generator.generate_quiz_from_prompt(
            "Create a personality quiz about what type of programmer someone is",
            "personality"
        )
        
        if quiz:
            console.print(f"[green]✅ AI generated quiz with {len(quiz.questions)} questions[/green]")
            
            # Show AI-generated outcomes
            console.print("\n[dim]AI-Generated Programmer Types:[/dim]")
            for outcome in list(quiz.outcomes.values())[:3]:
                console.print(f"  {outcome.emoji} {outcome.name}")
        else:
            console.print("[yellow]⚠️  AI quiz generation failed[/yellow]")
        
    except Exception as e:
        console.print(f"[red]❌ AI generation demo failed: {e}[/red]")


def demo_statistics():
    """Demo quiz statistics"""
    console.print("\n[bold yellow]📊 Quiz Statistics Demo[/bold yellow]")
    
    try:
        config = load_config()
        template_manager = QuizTemplateManager(config)
        
        # Get stats for different quiz types
        quiz_stats = []
        
        for template_id in ['harry_potter_house', 'marvel_character']:
            quiz = template_manager.create_quiz_from_template(template_id)
            if quiz:
                stats = quiz.get_quiz_stats()
                stats['template'] = template_id
                quiz_stats.append(stats)
        
        if quiz_stats:
            table = Table(title="Quiz Statistics Comparison")
            table.add_column("Template", style="cyan")
            table.add_column("Type", style="blue")
            table.add_column("Questions", style="yellow")
            table.add_column("Outcomes", style="green")
            table.add_column("Est. Posts", style="red")
            table.add_column("Viral Potential", style="magenta")
            
            for stats in quiz_stats:
                table.add_row(
                    stats['template'].replace('_', ' ').title(),
                    stats['quiz_type'],
                    str(stats['total_questions']),
                    str(stats['total_outcomes']),
                    str(stats['estimated_posts']),
                    stats['viral_potential']
                )
            
            console.print(table)
        else:
            console.print("[yellow]No quiz statistics available[/yellow]")
        
    except Exception as e:
        console.print(f"[red]❌ Statistics demo failed: {e}[/red]")


def demo_viral_potential():
    """Demo viral potential analysis"""
    console.print("\n[bold magenta]🔥 Viral Potential Demo[/bold magenta]")
    
    viral_factors = [
        ("🎯 Personality Results", "People love discovering things about themselves"),
        ("📱 Social Sharing", "Built-in share text and hashtags for each result"),
        ("🔄 Repeat Engagement", "Friends challenge each other to take the quiz"),
        ("📊 Multiple Outcomes", "4-8 different results keep content fresh"),
        ("⚡ Quick Completion", "2-3 minute quizzes perfect for social media"),
        ("🎨 Visual Appeal", "Emoji and color schemes make results shareable"),
        ("📈 Algorithmic Boost", "High engagement signals boost reach"),
        ("💬 Comment Generation", "Results spark discussions and debates")
    ]
    
    console.print("[green]Why Quiz Content Goes Viral:[/green]\n")
    
    for factor, explanation in viral_factors:
        console.print(f"  {factor} - {explanation}")
    
    console.print(f"\n[bold]🚀 Estimated Reach Potential:[/bold]")
    console.print("  • Single quiz post: 1,000-10,000 views")
    console.print("  • Viral quiz series: 50,000-500,000 views")
    console.print("  • Celebrity/influencer sharing: 1M+ views")


def show_demo_summary():
    """Show demo summary"""
    console.print("\n" + "="*60)
    console.print(Panel.fit(
        "[bold magenta]🎉 Quiz System Demo Complete![/bold magenta]\n\n"
        "[white]The quiz system is ready to create viral content![/white]\n\n"
        "[bold]Key Features Demonstrated:[/bold]\n"
        "[green]✅[/green] Pre-built viral quiz templates\n"
        "[green]✅[/green] AI-powered quiz generation\n"
        "[green]✅[/green] Multiple quiz types (personality, knowledge, compatibility)\n"
        "[green]✅[/green] Automatic social media optimization\n"
        "[green]✅[/green] Viral potential analysis\n"
        "[green]✅[/green] Scalable content generation\n\n"
        "[bold]Ready to Create Viral Quizzes:[/bold]\n"
        "[cyan]1.[/cyan] Use templates: [bold]pixi run quiz-wizard templates[/bold]\n"
        "[cyan]2.[/cyan] Create custom quiz: [bold]pixi run quiz-wizard create[/bold]\n"
        "[cyan]3.[/cyan] Generate ideas: [bold]pixi run quiz-wizard ideas <theme>[/bold]\n"
        "[cyan]4.[/cyan] Launch GUI: [bold]pixi run run[/bold]\n\n"
        "[dim]Start building your viral quiz empire! 🧠✨[/dim]",
        border_style="green"
    ))


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def main():
    """Main function for non-typer usage"""
    full()


if __name__ == "__main__":
    if RICH_AVAILABLE:
        app()
    else:
        main()
