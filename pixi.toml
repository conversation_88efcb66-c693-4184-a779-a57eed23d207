[project]
name = "cyoa_automation"
version = "0.1.0"
description = "Choose Your Own Adventure automation system for X (Twitter)"
channels = ["conda-forge", "pytorch"]
platforms = ["linux-64", "osx-64", "win-64"]

[dependencies]
python = "3.10"
tweepy = ">=4.14.0"
moviepy = ">=1.0.3"
python-dotenv = ">=1.0.0"
ffmpeg-python = ">=0.2.0"
requests = ">=2.28.0"
pyqt5 = ">=5.15.9"
pygraphviz = ">=1.11"
torch = ">=2.0.0"
pyttsx3 = ">=2.90"
numpy = ">=1.24.0"
pytest = ">=7.4.0"
networkx = ">=3.1"
scikit-learn = ">=1.3.0"
matplotlib = ">=3.7.0"
pillow = ">=10.0.0"
opencv-python = ">=4.8.0"
librosa = ">=0.10.0"
soundfile = ">=0.12.0"
psutil = ">=5.9.0"
rich = ">=13.0.0"
typer = ">=0.9.0"
watchdog = ">=3.0.0"
pydantic = ">=2.0.0"
lmstudio = ">=0.2.0"

[tasks]
# Core application tasks
install = "pixi install"
run = "python main.py"
wizard = "python scripts/story_wizard_cli.py"
quiz-wizard = "python scripts/quiz_wizard_cli.py"
health = "python scripts/health_check.py"

# Development and testing
test = "pytest tests/ -v"
test-all = "python scripts/test_complete_system.py"
test-story = "python scripts/test_generation.py"
test-character = "python scripts/test_character_system.py"
demo = "python scripts/demo.py"

# Story operations
generate = "python scripts/generate_story.py"
validate = "python scripts/validate_story.py"
backup = "python scripts/backup_stories.py"

# Media and social
post = "python scripts/post_to_x.py"
media = "python scripts/generate_media.py"

# External services
start-lmstudio = "echo 'Please start LM Studio manually and load a model'"
start-comfyui = "python ComfyUI/main.py --listen 127.0.0.1"
check-lmstudio = "curl -s http://localhost:1234/v1/models || echo 'LM Studio not running'"
check-comfyui = "curl -s http://127.0.0.1:8188/system_stats || echo 'ComfyUI not running'"

# Setup and maintenance
setup = { cmd = "python scripts/setup.py", depends_on = ["install"] }
first-run = { cmd = "python scripts/first_run_setup.py", depends_on = ["install"] }
update-models = "python scripts/update_models.py"

# Code quality
lint = "flake8 src/ tests/"
format = "black src/ tests/"
type-check = "mypy src/"

# Documentation
docs = "python scripts/generate_docs.py"
examples = "python scripts/create_examples.py"

# Analytics and Optimization
analytics = "python scripts/analytics_cli.py"
analyze = "python scripts/analytics_cli.py analyze"
optimize = "python scripts/analytics_cli.py optimize"
ab-test = "python scripts/analytics_cli.py ab-test"

# Authentication and Rate Limiting
auth = "python scripts/auth_cli.py"
login = "python scripts/auth_cli.py login"
auth-status = "python scripts/auth_cli.py status"
auth-setup = "python scripts/auth_cli.py setup"

# LM Studio Management
lmstudio = "python scripts/lmstudio_cli.py"
lm-status = "python scripts/lmstudio_cli.py status"
lm-models = "python scripts/lmstudio_cli.py models"
lm-test = "python scripts/lmstudio_cli.py test"
lm-benchmark = "python scripts/lmstudio_cli.py benchmark"

[feature.dev.dependencies]
black = ">=23.0.0"
flake8 = ">=6.0.0"
pytest-cov = ">=4.1.0"

[environments]
default = { solve-group = "default" }
dev = { features = ["dev"], solve-group = "default" }
