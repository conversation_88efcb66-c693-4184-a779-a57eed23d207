[project]
name = "cyoa_automation"
version = "0.1.0"
description = "Choose Your Own Adventure automation system for X (Twitter)"
channels = ["conda-forge", "pytorch"]
platforms = ["linux-64", "osx-64", "win-64"]

[dependencies]
python = "3.10"
tweepy = ">=4.14.0"
moviepy = ">=1.0.3"
python-dotenv = ">=1.0.0"
ffmpeg-python = ">=0.2.0"
requests = ">=2.28.0"
pyqt5 = ">=5.15.9"
pygraphviz = ">=1.11"
torch = ">=2.0.0"
pyttsx3 = ">=2.90"
numpy = ">=1.24.0"
pytest = ">=7.4.0"
networkx = ">=3.1"
scikit-learn = ">=1.3.0"
matplotlib = ">=3.7.0"
pillow = ">=10.0.0"
opencv-python = ">=4.8.0"
librosa = ">=0.10.0"
soundfile = ">=0.12.0"

[tasks]
install = "pixi install"
run = "python main.py"
test = "pytest tests/ -v"
generate = "python -m scripts.generate_story"
post = "python -m scripts.post_to_x"
start_ollama = "ollama serve"
start_comfyui = "python ComfyUI/main.py --listen 127.0.0.1"
setup = { cmd = "python scripts/setup.py", depends_on = ["install"] }
lint = "flake8 src/ tests/"
format = "black src/ tests/"

[feature.dev.dependencies]
black = ">=23.0.0"
flake8 = ">=6.0.0"
pytest-cov = ">=4.1.0"

[environments]
default = { solve-group = "default" }
dev = { features = ["dev"], solve-group = "default" }
