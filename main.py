#!/usr/bin/env python3
"""
CYOA Automation System - Main Entry Point
Choose Your Own Adventure automation for X (Twitter) with local AI models
"""

import sys
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
import dotenv

# Import our modules
from utils.lmstudio_client import LMStudioClient
from story.story_generator import StoryGenerator
from story.inventory_system import InventoryManager
from story.class_system import ClassManager
from story.scoring_system import ScoringSystem
from story.rating_system import RatingSystem
from gui.main_window import MainWindow


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "cyoa_automation.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Create separate loggers for different components
    loggers = [
        "cyoa.story",
        "cyoa.media", 
        "cyoa.social",
        "cyoa.gui",
        "cyoa.utils"
    ]
    
    for logger_name in loggers:
        logger = logging.getLogger(logger_name)
        handler = logging.FileHandler(log_dir / f"{logger_name.split('.')[-1]}.log")
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(handler)


def load_config() -> Dict[str, Any]:
    """Load configuration from config.json"""
    try:
        config_path = Path("config.json")
        if not config_path.exists():
            raise FileNotFoundError("config.json not found")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        logging.info("Configuration loaded successfully")
        return config
        
    except Exception as e:
        logging.error(f"Error loading configuration: {e}")
        # Return default configuration
        return {
            "story_generation": {
                "num_entry_points": 3,
                "min_endings": 5,
                "max_nodes": 100
            },
            "inventory_system": {
                "initial_inventory": {"gold": 100}
            },
            "class_system": {
                "classes": ["Mage", "Ranger", "Charmer"]
            },
            "lmstudio": {
                "base_url": "http://localhost:1234",
                "default_model": "local-model",
                "timeout": 120
            }
        }


def load_environment() -> None:
    """Load environment variables"""
    env_path = Path(".env")
    if env_path.exists():
        dotenv.load_dotenv(env_path)
        logging.info("Environment variables loaded from .env")
    else:
        logging.warning(".env file not found, using system environment variables")


def create_directories(config: Dict[str, Any]) -> None:
    """Create necessary directories"""
    paths = config.get("paths", {})
    
    directories = [
        paths.get("videos_dir", "videos"),
        paths.get("logs_dir", "logs"),
        paths.get("data_dir", "data"),
        paths.get("storylines_dir", "data/storylines"),
        paths.get("workflows_dir", "workflows")
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logging.info("Created necessary directories")


def check_dependencies(config: Dict[str, Any]) -> bool:
    """Check if required dependencies are available"""
    errors = []
    
    # Check LM Studio
    try:
        lmstudio_config = config.get("lmstudio", {})
        lmstudio_client = LMStudioClient(config)

        if not lmstudio_client.is_available():
            errors.append("LM Studio server is not available. Please start LM Studio and load a model")
    except Exception as e:
        errors.append(f"Error checking LM Studio: {e}")
    
    # Check ComfyUI (optional for now)
    try:
        import requests
        comfyui_config = config.get("comfyui", {})
        comfyui_url = comfyui_config.get("base_url", "http://127.0.0.1:8188")
        
        response = requests.get(f"{comfyui_url}/system_stats", timeout=5)
        if response.status_code != 200:
            logging.warning("ComfyUI server not available (optional)")
    except Exception:
        logging.warning("ComfyUI server not available (optional)")
    
    if errors:
        for error in errors:
            logging.error(error)
        return False
    
    return True


class CYOAApplication:
    """Main application class"""
    
    def __init__(self):
        self.config = None
        self.lmstudio_client = None
        self.story_generator = None
        self.inventory_manager = None
        self.class_manager = None
        self.scoring_system = None
        self.rating_system = None
        self.main_window = None
    
    def initialize(self) -> bool:
        """Initialize the application"""
        try:
            # Load configuration and environment
            self.config = load_config()
            load_environment()
            
            # Setup logging
            log_level = os.getenv("LOG_LEVEL", "INFO")
            setup_logging(log_level)
            
            # Create directories
            create_directories(self.config)
            
            # Check dependencies
            if not check_dependencies(self.config):
                return False
            
            # Initialize core components
            self._initialize_components()
            
            logging.info("CYOA Automation System initialized successfully")
            return True
            
        except Exception as e:
            logging.error(f"Error initializing application: {e}")
            return False
    
    def _initialize_components(self):
        """Initialize core system components"""
        # Initialize LM Studio client
        self.lmstudio_client = LMStudioClient(self.config)

        # Initialize story systems
        self.story_generator = StoryGenerator(self.config, self.lmstudio_client)
        self.inventory_manager = InventoryManager(self.config)
        self.class_manager = ClassManager(self.config)
        self.scoring_system = ScoringSystem(self.config)
        self.rating_system = RatingSystem(self.config)

        logging.info("Core components initialized")
    
    def run_gui(self, app: QApplication) -> int:
        """Run the GUI application"""
        try:
            # Create main window
            self.main_window = MainWindow(
                config=self.config,
                lmstudio_client=self.lmstudio_client,
                story_generator=self.story_generator,
                inventory_manager=self.inventory_manager,
                class_manager=self.class_manager,
                scoring_system=self.scoring_system,
                rating_system=self.rating_system
            )
            
            self.main_window.show()
            
            # Setup periodic tasks
            self._setup_periodic_tasks()
            
            logging.info("GUI application started")
            return app.exec_()
            
        except Exception as e:
            logging.error(f"Error running GUI: {e}")
            QMessageBox.critical(None, "Error", f"Failed to start GUI: {e}")
            return 1
    
    def _setup_periodic_tasks(self):
        """Setup periodic background tasks"""
        # Check LM Studio connection every 30 seconds
        self.lmstudio_timer = QTimer()
        self.lmstudio_timer.timeout.connect(self._check_lmstudio_connection)
        self.lmstudio_timer.start(30000)  # 30 seconds

    def _check_lmstudio_connection(self):
        """Periodically check LM Studio connection"""
        try:
            if not self.lmstudio_client.is_available():
                logging.warning("LM Studio connection lost")
                if self.main_window:
                    self.main_window.update_status("LM Studio connection lost", "error")
            else:
                current_model = self.lmstudio_client.get_current_model()
                if self.main_window:
                    if current_model:
                        self.main_window.update_status(f"LM Studio connected: {current_model}", "success")
                    else:
                        self.main_window.update_status("LM Studio connected (no model)", "warning")
        except Exception as e:
            logging.error(f"Error checking LM Studio connection: {e}")


def main():
    """Main entry point"""
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("CYOA Automation System")
        app.setApplicationVersion("0.1.0")
        
        # Create and initialize application
        cyoa_app = CYOAApplication()
        
        if not cyoa_app.initialize():
            QMessageBox.critical(
                None, 
                "Initialization Error", 
                "Failed to initialize CYOA Automation System. Check logs for details."
            )
            return 1
        
        # Run the application
        return cyoa_app.run_gui(app)
        
    except KeyboardInterrupt:
        logging.info("Application interrupted by user")
        return 0
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
