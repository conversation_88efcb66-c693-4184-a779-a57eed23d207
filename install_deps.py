#!/usr/bin/env python3
"""
Dependency Installation Script
Installs missing Python packages that aren't available in conda
"""

import subprocess
import sys
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def install_pip_packages():
    """Install packages via pip that aren't available in conda"""
    pip_packages = [
        "python-dotenv",  # Environment variable loading
        "lmstudio-python",  # LM Studio SDK (if available)
    ]
    
    print("🔧 Installing additional packages via pip...")
    
    for package in pip_packages:
        success = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}, continuing anyway...")
    
    print("✅ Pip package installation completed")

def create_directories():
    """Create necessary directories"""
    directories = [
        "data",
        "data/storylines", 
        "data/auth",
        "data/posting",
        "logs",
        "videos",
        "workflows"
    ]
    
    print("📁 Creating necessary directories...")
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def create_config():
    """Create default configuration file"""
    config_path = Path("config.json")
    
    if config_path.exists():
        print("⚠️ config.json already exists, skipping creation")
        return
    
    default_config = """{
  "story_generation": {
    "num_entry_points": 3,
    "min_endings": 5,
    "max_nodes": 100
  },
  "inventory_system": {
    "initial_inventory": {"gold": 100}
  },
  "class_system": {
    "classes": ["Mage", "Ranger", "Charmer"]
  },
  "lmstudio": {
    "base_url": "http://localhost:1234",
    "default_model": "local-model",
    "timeout": 120,
    "max_tokens": 2048,
    "temperature": 0.7
  },
  "comfyui": {
    "base_url": "http://127.0.0.1:8188"
  },
  "x_auth": {
    "redirect_uri": "http://localhost:8080/callback"
  }
}"""
    
    print("📝 Creating default configuration file...")
    
    try:
        with open(config_path, 'w') as f:
            f.write(default_config)
        print("✅ Created config.json")
    except Exception as e:
        print(f"❌ Failed to create config.json: {e}")

def create_env_template():
    """Create .env template file"""
    env_path = Path(".env.template")
    
    env_template = """# X (Twitter) API Credentials
# Get these from https://developer.twitter.com/
X_API_KEY=your_api_key_here
X_API_SECRET=your_api_secret_here
X_ACCESS_TOKEN=your_access_token_here
X_ACCESS_TOKEN_SECRET=your_access_token_secret_here

# LM Studio Configuration
LMSTUDIO_BASE_URL=http://localhost:1234

# ComfyUI Configuration (Optional)
COMFYUI_BASE_URL=http://127.0.0.1:8188

# Logging Level
LOG_LEVEL=INFO
"""
    
    print("📝 Creating .env template file...")
    
    try:
        with open(env_path, 'w') as f:
            f.write(env_template)
        print("✅ Created .env.template")
        print("💡 Copy .env.template to .env and fill in your API credentials")
    except Exception as e:
        print(f"❌ Failed to create .env.template: {e}")

def main():
    """Main installation function"""
    print("🚀 CYOA Automation System - Dependency Installation")
    print("=" * 50)
    
    # Install pip packages
    install_pip_packages()
    
    # Create directories
    create_directories()
    
    # Create configuration files
    create_config()
    create_env_template()
    
    print("\n" + "=" * 50)
    print("🎉 Installation completed!")
    print("\n📋 Next steps:")
    print("1. Install LM Studio from https://lmstudio.ai")
    print("2. Download and load a model in LM Studio")
    print("3. Copy .env.template to .env and add your X API credentials")
    print("4. Run: pixi run run")
    print("\n💡 For help, run: pixi run lm-status")

if __name__ == "__main__":
    main()
